import { MainNodeResponse } from './mainNodeResponse';
export declare class MainValueResponse {
    'array'?: Array<MainValueResponse>;
    '_boolean'?: boolean;
    'contactProperty'?: string;
    'date'?: string;
    'eventProperty'?: string;
    'expression'?: MainNodeResponse;
    'number'?: number;
    'string'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
