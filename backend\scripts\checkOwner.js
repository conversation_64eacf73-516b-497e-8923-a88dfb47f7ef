import mongoose from 'mongoose';
import User from '../models/User.js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

const checkOwner = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    
    const owner = await User.findOne({ role: 'owner' }).select('+password');
    if (owner) {
      console.log('✅ Owner found in database:');
      console.log('- Name:', owner.name);
      console.log('- Email:', owner.email);
      console.log('- Role:', owner.role);
      console.log('- Active:', owner.isActive);
      console.log('- Email Verified:', owner.emailVerified);
      console.log('- Phone Verified:', owner.phoneVerified);
      console.log('- Created:', owner.createdAt);
      console.log('- Password field exists:', !!owner.password);
      console.log('- Password length:', owner.password ? owner.password.length : 'N/A');

      // Test password using the model method
      if (owner.password) {
        try {
          const testPassword = '123456';
          const isPasswordValid = await owner.comparePassword(testPassword);
          console.log('- Password test (123456):', isPasswordValid ? '✅ Valid' : '❌ Invalid');
        } catch (error) {
          console.log('- Password test error:', error.message);
        }
      } else {
        console.log('❌ Password field is missing!');
      }
      
    } else {
      console.log('❌ No owner found in database');
    }
    
  } catch (error) {
    console.error('Error checking owner:', error);
  } finally {
    mongoose.disconnect();
  }
};

checkOwner();
