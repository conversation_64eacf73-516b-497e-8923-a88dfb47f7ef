import nodemailer from 'nodemailer';
import { emailTemplates } from '../templates/emailTemplates.js';

/**
 * Comprehensive Email Service for Zina Chop House
 * Supports both Brevo (primary) and Nodemailer (fallback)
 */
class EmailService {
  constructor() {
    this.brevoClient = null;
    this.nodemailerTransporter = null;
    this.isBrevoAvailable = false;
    this.isNodemailerAvailable = false;
    
    this.initialize();
  }

  /**
   * Initialize email services
   */
  async initialize() {
    // Try to initialize Brevo first
    await this.initializeBrevo();
    
    // Initialize Nodemailer as fallback
    await this.initializeNodemailer();
    
    console.log(`Email Service initialized - Brevo: ${this.isBrevoAvailable}, Nodemailer: ${this.isNodemailerAvailable}`);
  }

  /**
   * Initialize Brevo email service
   */
  async initializeBrevo() {
    try {
      // Try to import Brevo SDK
      const { TransactionalEmailsApi, TransactionalEmailsApiApiKeys } = await import('@getbrevo/brevo');
      
      if (!process.env.BREVO_API_KEY) {
        console.warn('BREVO_API_KEY not found in environment variables');
        return;
      }

      this.brevoClient = new TransactionalEmailsApi();
      this.brevoClient.setApiKey(TransactionalEmailsApiApiKeys.apiKey, process.env.BREVO_API_KEY);
      
      this.isBrevoAvailable = true;
      console.log('✅ Brevo email service initialized successfully');
    } catch (error) {
      console.warn('⚠️ Brevo initialization failed:', error.message);
      this.isBrevoAvailable = false;
    }
  }

  /**
   * Initialize Nodemailer as fallback
   */
  async initializeNodemailer() {
    try {
      if (!process.env.EMAIL_HOST || !process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        console.warn('Email configuration missing for Nodemailer');
        return;
      }

      this.nodemailerTransporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST,
        port: parseInt(process.env.EMAIL_PORT) || 587,
        secure: process.env.EMAIL_PORT === '465',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });

      // Verify connection
      await this.nodemailerTransporter.verify();
      this.isNodemailerAvailable = true;
      console.log('✅ Nodemailer initialized successfully');
    } catch (error) {
      console.warn('⚠️ Nodemailer initialization failed:', error.message);
      this.isNodemailerAvailable = false;
    }
  }

  /**
   * Send email using the best available service
   */
  async sendEmail(emailData) {
    const { to, subject, html, text, templateId, templateData, attachments } = emailData;

    // Validate required fields
    if (!to || !subject || (!html && !text && !templateId)) {
      throw new Error('Missing required email fields: to, subject, and content');
    }

    // Try Brevo first
    if (this.isBrevoAvailable) {
      try {
        return await this.sendWithBrevo(emailData);
      } catch (error) {
        console.error('Brevo send failed, falling back to Nodemailer:', error.message);
      }
    }

    // Fallback to Nodemailer
    if (this.isNodemailerAvailable) {
      return await this.sendWithNodemailer(emailData);
    }

    throw new Error('No email service available');
  }

  /**
   * Send email using Brevo
   */
  async sendWithBrevo(emailData) {
    const { to, subject, html, text, templateId, templateData, attachments, from } = emailData;

    const sendSmtpEmail = {
      to: Array.isArray(to) ? to.map(email => ({ email })) : [{ email: to }],
      subject,
      sender: {
        name: process.env.BUSINESS_NAME || 'Zina Chop House',
        email: from || process.env.EMAIL_FROM || process.env.EMAIL_USER
      }
    };

    // Use template if provided
    if (templateId) {
      sendSmtpEmail.templateId = templateId;
      sendSmtpEmail.params = templateData || {};
    } else {
      // Use HTML or text content
      if (html) sendSmtpEmail.htmlContent = html;
      if (text) sendSmtpEmail.textContent = text;
    }

    // Add attachments if provided
    if (attachments && attachments.length > 0) {
      sendSmtpEmail.attachment = attachments.map(att => ({
        name: att.filename,
        content: att.content,
        contentType: att.contentType
      }));
    }

    const result = await this.brevoClient.sendTransacEmail(sendSmtpEmail);
    
    return {
      success: true,
      messageId: result.messageId,
      service: 'brevo'
    };
  }

  /**
   * Send email using Nodemailer
   */
  async sendWithNodemailer(emailData) {
    const { to, subject, html, text, attachments, from } = emailData;

    const mailOptions = {
      from: from || process.env.EMAIL_FROM || process.env.EMAIL_USER,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
      text,
      attachments
    };

    const result = await this.nodemailerTransporter.sendMail(mailOptions);
    
    return {
      success: true,
      messageId: result.messageId,
      service: 'nodemailer'
    };
  }

  /**
   * Send order confirmation email
   */
  async sendOrderConfirmation(orderData) {
    const { customerEmail, customerName, orderId, items, totalAmount, deliveryAddress, estimatedDeliveryTime } = orderData;

    const html = emailTemplates.orderConfirmation({
      customerName,
      orderId,
      items,
      totalAmount,
      deliveryAddress,
      estimatedDeliveryTime,
      orderDate: new Date().toLocaleDateString()
    });

    return await this.sendEmail({
      to: customerEmail,
      subject: `Order Confirmation - ${orderId}`,
      html,
      text: `Thank you for your order, ${customerName}! Your order ${orderId} has been confirmed and will be delivered in approximately ${estimatedDeliveryTime} minutes.`
    });
  }

  /**
   * Send order status update email
   */
  async sendOrderStatusUpdate(orderData) {
    const { customerEmail, customerName, orderId, status, estimatedTime, message } = orderData;

    const statusMessages = {
      'confirmed': 'Your order has been confirmed and is being prepared.',
      'preparing': 'Our chefs are preparing your delicious meal.',
      'ready': 'Your order is ready and will be delivered soon.',
      'out_for_delivery': 'Your order is on its way!',
      'delivered': 'Your order has been delivered. Enjoy your meal!',
      'cancelled': 'Your order has been cancelled. If you have any questions, please contact us.'
    };

    const html = emailTemplates.orderStatusUpdate({
      customerName,
      orderId,
      status,
      estimatedTime,
      message: message || statusMessages[status]
    });

    return await this.sendEmail({
      to: customerEmail,
      subject: `Order Update - ${orderId}`,
      html,
      text: `Hello ${customerName}! Your order ${orderId} status has been updated to: ${status.replace('_', ' ')}. ${statusMessages[status] || ''}`
    });
  }

  /**
   * Send special order notification to owner
   */
  async sendSpecialOrderNotification(orderData) {
    const { customerName, customerPhone, customerEmail, orderType, details, eventDate, numberOfPeople, notes } = orderData;

    const html = emailTemplates.specialOrderNotification({
      customerName,
      customerPhone,
      customerEmail,
      orderType,
      details,
      eventDate,
      numberOfPeople,
      notes,
      submissionDate: new Date().toLocaleString()
    });

    return await this.sendEmail({
      to: process.env.OWNER_EMAIL,
      subject: `New Special Order Request - ${orderType}`,
      html,
      text: `New special order from ${customerName} (${customerPhone}). Type: ${orderType}. Details: ${details}`
    });
  }

  /**
   * Send welcome email to new customers
   */
  async sendWelcomeEmail(customerData) {
    const { customerEmail, customerName } = customerData;

    const html = emailTemplates.welcome({
      customerName,
      customerEmail
    });

    return await this.sendEmail({
      to: customerEmail,
      subject: 'Welcome to Zina Chop House! 🎉',
      html,
      text: `Welcome to Zina Chop House, ${customerName}! We're excited to serve you authentic Cameroonian cuisine. Browse our menu and place your first order today!`
    });
  }

  /**
   * Send welcome email to new owner
   */
  async sendOwnerWelcomeEmail(ownerData) {
    const { ownerEmail, ownerName, password } = ownerData;

    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Zina Chop House - Owner Account</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .credentials { background: #fff; padding: 20px; border-radius: 8px; border-left: 4px solid #f97316; margin: 20px 0; }
          .button { display: inline-block; padding: 12px 24px; background-color: #f97316; color: white; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🏪 Welcome to Zina Chop House</h1>
            <p>Your Owner Account Has Been Created Successfully!</p>
          </div>
          <div class="content">
            <h2>Hello ${ownerName}!</h2>
            <p>Congratulations! Your owner account for Zina Chop House has been successfully created. You now have full administrative access to manage your restaurant.</p>

            <div class="credentials">
              <h3>🔐 Your Login Credentials:</h3>
              <p><strong>Email:</strong> ${ownerEmail}</p>
              <p><strong>Password:</strong> ${password}</p>
              <p><em>Please keep these credentials secure and consider changing your password after first login.</em></p>
            </div>

            <h3>🎯 What You Can Do:</h3>
            <ul>
              <li>📋 Manage menu items and pricing</li>
              <li>📦 Track and manage orders</li>
              <li>👥 Manage customers and delivery partners</li>
              <li>📊 View sales analytics and reports</li>
              <li>⭐ Monitor customer feedback</li>
              <li>🚚 Handle special orders</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.CLIENT_URL || 'http://localhost:5173'}/login" class="button">
                Login to Dashboard
              </a>
            </div>

            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

            <p>Welcome aboard!</p>
            <p><strong>The Zina Chop House Team</strong></p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendEmail({
      to: ownerEmail,
      subject: '🏪 Welcome to Zina Chop House - Owner Account Created',
      html,
      text: `Welcome to Zina Chop House, ${ownerName}! Your owner account has been created. Login credentials: Email: ${ownerEmail}, Password: ${password}. You can now manage your restaurant at ${process.env.CLIENT_URL || 'http://localhost:5173'}/login`
    });
  }

  /**
   * Send promotional email
   */
  async sendPromotionalEmail(emailData) {
    const { recipients, subject, content, templateId, templateData } = emailData;

    const promises = recipients.map(recipient =>
      this.sendEmail({
        to: recipient.email,
        subject,
        html: content,
        templateId,
        templateData: { ...templateData, customerName: recipient.name }
      })
    );

    const results = await Promise.allSettled(promises);

    return {
      sent: results.filter(r => r.status === 'fulfilled').length,
      failed: results.filter(r => r.status === 'rejected').length,
      results
    };
  }

  /**
   * Check service health
   */
  async checkHealth() {
    return {
      brevo: {
        available: this.isBrevoAvailable,
        status: this.isBrevoAvailable ? 'healthy' : 'unavailable'
      },
      nodemailer: {
        available: this.isNodemailerAvailable,
        status: this.isNodemailerAvailable ? 'healthy' : 'unavailable'
      }
    };
  }
}

// Create and export singleton instance
const emailService = new EmailService();
export default emailService;
