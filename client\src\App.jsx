import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { Toaster } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { store } from './store/store';
import './i18n';
import { initializeLanguage } from './utils/languageUtils';

// Layout Components
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Pages
import Home from './pages/Home';
import Menu from './pages/Menu';
import MenuItemDetail from './pages/MenuItemDetail';
import Cart from './pages/Cart';
import Checkout from './pages/Checkout';
import Orders from './pages/Orders';
import OrderDetail from './pages/OrderDetail';
import SpecialOrders from './pages/SpecialOrders';
import Profile from './pages/Profile';
import Feedback from './pages/Feedback';
// Auth Pages
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';

// Owner Dashboard
import OwnerDashboard from './pages/OwnerDashboard';
import MenuManagement from './pages/owner/MenuManagement';
import OrderManagement from './pages/owner/OrderManagement';
import SpecialOrderManagement from './pages/owner/SpecialOrderManagement';
import FeedbackManagement from './pages/owner/FeedbackManagement';
import UserManagement from './pages/owner/UserManagement';

// Delivery Dashboard
import DeliveryDashboard from './pages/delivery/Dashboard';
import DeliveryOrders from './pages/delivery/Orders';

// Customer Dashboard
import CustomerDashboard from './pages/customer/Dashboard';

// App content component to use hooks inside Provider
const AppContent = () => {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Initialize language system
    initializeLanguage(i18n);
  }, [i18n]);

  return (
    <Router>
        <div className="App min-h-screen bg-gray-50">
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />

          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<Layout />}>
              <Route index element={<Home />} />
              <Route path="menu" element={<Menu />} />
              <Route path="menu/:id" element={<MenuItemDetail />} />
              <Route path="special-orders" element={<SpecialOrders />} />
            </Route>

            {/* Auth Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />

            {/* Protected Routes for All Authenticated Users */}
            <Route path="/" element={<ProtectedRoute />}>
              <Route path="/" element={<Layout />}>
                <Route path="profile" element={<Profile />} />
              </Route>
            </Route>

            {/* Protected Customer Routes */}
            <Route path="/" element={<ProtectedRoute allowedRoles={['customer']} />}>
              <Route path="/" element={<Layout />}>
                <Route path="dashboard" element={<CustomerDashboard />} />
                <Route path="cart" element={<Cart />} />
                <Route path="checkout" element={<Checkout />} />
                <Route path="orders" element={<Orders />} />
                <Route path="orders/:id" element={<OrderDetail />} />
                <Route path="feedback/:orderId" element={<Feedback />} />
              </Route>
            </Route>

            {/* Owner Routes - Protected */}
            <Route path="/owner" element={<ProtectedRoute allowedRoles={['owner']} />}>
              <Route index element={<OwnerDashboard />} />
              <Route path="menu" element={<MenuManagement />} />
              <Route path="orders" element={<OrderManagement />} />
              <Route path="special-orders" element={<SpecialOrderManagement />} />
              <Route path="feedback" element={<FeedbackManagement />} />
              <Route path="users" element={<UserManagement />} />
            </Route>

            {/* Delivery Routes - Protected */}
            <Route path="/delivery" element={<ProtectedRoute allowedRoles={['delivery']} />}>
              <Route index element={<DeliveryDashboard />} />
              <Route path="orders" element={<DeliveryOrders />} />
            </Route>

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    );
};

function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

export default App;
