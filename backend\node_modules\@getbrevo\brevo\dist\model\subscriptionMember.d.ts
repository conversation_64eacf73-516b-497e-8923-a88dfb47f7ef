export declare class SubscriptionMember {
    'createdAt'?: string;
    'memberContactIds'?: Array<number>;
    'organizationId'?: number;
    'ownerContactId'?: number;
    'updatedAt'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
