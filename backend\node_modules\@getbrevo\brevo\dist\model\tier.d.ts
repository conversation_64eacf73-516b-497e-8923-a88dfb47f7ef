import { TierAccessConditionsInner } from './tierAccessConditionsInner';
import { TierTierRewardsInner } from './tierTierRewardsInner';
export declare class Tier {
    'tierId'?: string;
    'name'?: string;
    'imageRef'?: string;
    'loyaltyProgramId'?: string;
    'groupId'?: string;
    'createdAt'?: Date;
    'updatedAt'?: Date;
    'accessConditions'?: Array<TierAccessConditionsInner>;
    'tierRewards'?: Array<TierTierRewardsInner>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
