import mongoose from 'mongoose';
import User from '../models/User.js';
import dotenv from 'dotenv';

dotenv.config();

const deleteOwner = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    
    const result = await User.deleteOne({ role: 'owner' });
    console.log('Owner deleted:', result.deletedCount > 0 ? 'Success' : 'No owner found');
    
  } catch (error) {
    console.error('Error deleting owner:', error);
  } finally {
    mongoose.disconnect();
  }
};

deleteOwner();
