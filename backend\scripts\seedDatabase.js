import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import User from '../models/User.js';
import MenuItem from '../models/MenuItem.js';
import Order from '../models/Order.js';
import SpecialOrder from '../models/SpecialOrder.js';
import Feedback from '../models/Feedback.js';


// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zina-chop-house');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Seed Users
const seedUsers = async () => {
  try {
    await User.deleteMany({});
    
    const users = [
      {
        name: '<PERSON><PERSON> House Owner',
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 12),
        phone: '+237123456789',
        role: 'owner',
        isVerified: true
      },
      {
        name: '<PERSON> Delivery',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 12),
        phone: '+237555123456',
        role: 'delivery',
        isVerified: true,
        deliveryInfo: {
          vehicleType: 'motorcycle',
          licenseNumber: 'DL123456',
          isAvailable: true
        }
      }
    ];

    const createdUsers = await User.insertMany(users);
    console.log('✅ Users seeded successfully');
    return createdUsers;
  } catch (error) {
    console.error('❌ Error seeding users:', error);
  }
};

// Seed Menu Items
const seedMenuItems = async () => {
  try {
    await MenuItem.deleteMany({});
    
    const menuItems = [
      {
        name: 'Fried Rice with Chicken',
        description: 'Delicious fried rice with tender chicken pieces, mixed vegetables, and aromatic spices',
        price: 2500,
        category: 'rice-dishes',
        images: [{ url: '/images/fried-rice.jpg', alt: 'Fried Rice with Chicken' }],
        isAvailable: true,
        preparationTime: 15,
        ingredients: ['Jasmine Rice', 'Chicken Breast', 'Mixed Vegetables', 'Soy Sauce', 'Garlic', 'Ginger'],
        nutritionalInfo: {
          calories: 450,
          protein: 25,
          carbs: 55,
          fat: 12,
          fiber: 3
        },
        allergens: ['Soy', 'Gluten'],
        spiceLevel: 'Medium'
      },
      {
        name: 'Water Fufu and Eru',
        description: 'Traditional Cameroon dish with water fufu and eru vegetables',
        price: 3000,
        category: 'traditional',
        images: [{ url: '/images/fufu-eru.jpg', alt: 'Water Fufu and Eru' }],
        isAvailable: true,
        preparationTime: 25,
        ingredients: ['Cassava', 'Eru leaves', 'Palm oil', 'Meat', 'Fish'],
        nutritionalInfo: {
          calories: 380,
          protein: 20,
          carbs: 45,
          fat: 15,
          fiber: 8
        },
        spiceLevel: 'Mild'
      },
      {
        name: 'Grilled Fish',
        description: 'Fresh fish grilled to perfection with local spices',
        price: 4000,
        category: 'grilled',
        images: [{ url: '/images/grilled-fish.jpg', alt: 'Grilled Fish' }],
        isAvailable: true,
        preparationTime: 20,
        ingredients: ['Fresh Fish', 'Local Spices', 'Lemon', 'Herbs'],
        nutritionalInfo: {
          calories: 320,
          protein: 35,
          carbs: 5,
          fat: 18,
          fiber: 1
        },
        spiceLevel: 'Medium'
      },
      {
        name: 'Jollof Rice',
        description: 'Flavorful West African jollof rice with spices and vegetables',
        price: 2800,
        category: 'rice-dishes',
        images: [{ url: '/images/jollof-rice.jpg', alt: 'Jollof Rice' }],
        isAvailable: true,
        preparationTime: 20,
        ingredients: ['Rice', 'Tomatoes', 'Onions', 'Spices', 'Vegetables'],
        nutritionalInfo: {
          calories: 420,
          protein: 8,
          carbs: 65,
          fat: 14,
          fiber: 4
        },
        spiceLevel: 'Medium'
      },
      {
        name: 'Ndole',
        description: 'Traditional Cameroonian ndole with groundnuts and meat',
        price: 3500,
        category: 'traditional',
        images: [{ url: '/images/ndole.jpg', alt: 'Ndole' }],
        isAvailable: true,
        preparationTime: 35,
        ingredients: ['Ndole leaves', 'Groundnuts', 'Meat', 'Fish', 'Spices'],
        nutritionalInfo: {
          calories: 480,
          protein: 28,
          carbs: 25,
          fat: 32,
          fiber: 6
        },
        spiceLevel: 'Medium'
      },
      {
        name: 'Fried Plantains',
        description: 'Sweet fried plantains, a perfect side dish',
        price: 1500,
        category: 'sides',
        images: [{ url: '/images/fried-plantains.jpg', alt: 'Fried Plantains' }],
        isAvailable: true,
        preparationTime: 10,
        ingredients: ['Plantains', 'Oil', 'Salt'],
        nutritionalInfo: {
          calories: 180,
          protein: 2,
          carbs: 35,
          fat: 8,
          fiber: 3
        },
        spiceLevel: 'None'
      },
      {
        name: 'Pepper Soup',
        description: 'Spicy traditional pepper soup with meat',
        price: 2000,
        category: 'soups',
        images: [{ url: '/images/pepper-soup.jpg', alt: 'Pepper Soup' }],
        isAvailable: true,
        preparationTime: 30,
        ingredients: ['Meat', 'Pepper', 'Spices', 'Vegetables'],
        nutritionalInfo: {
          calories: 250,
          protein: 22,
          carbs: 8,
          fat: 15,
          fiber: 2
        },
        spiceLevel: 'Hot'
      },
      {
        name: 'Suya',
        description: 'Spicy grilled meat skewers with traditional spices',
        price: 1800,
        category: 'grilled',
        images: [{ url: '/images/suya.jpg', alt: 'Suya' }],
        isAvailable: true,
        preparationTime: 15,
        ingredients: ['Beef', 'Suya spice', 'Onions'],
        nutritionalInfo: {
          calories: 280,
          protein: 25,
          carbs: 8,
          fat: 18,
          fiber: 1
        },
        spiceLevel: 'Hot'
      }
    ];

    await MenuItem.insertMany(menuItems);
    console.log('✅ Menu items seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding menu items:', error);
  }
};

// Clear orders to ensure empty order history
const clearOrders = async () => {
  try {
    await Order.deleteMany({});
    await SpecialOrder.deleteMany({});
    await Feedback.deleteMany({});
    console.log('✅ Cleared all orders, special orders, and feedback');
  } catch (error) {
    console.error('❌ Error clearing orders:', error);
  }
};

// Main seeding function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    await connectDB();

    await seedUsers();
    await seedMenuItems();
    await clearOrders();
    
    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Initial Login Credentials:');
    console.log('👨‍💼 Owner: <EMAIL> / 123456');
    console.log('🚚 Delivery: <EMAIL> / password123');
    console.log('\n📝 Note: Customers can register through the application interface.');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase();
}

export default seedDatabase;
