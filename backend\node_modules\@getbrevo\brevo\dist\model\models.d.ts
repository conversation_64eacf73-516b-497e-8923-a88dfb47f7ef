import localVarRequest from 'request';
export * from './abTestCampaignResult';
export * from './abTestCampaignResultClickedLinks';
export * from './abTestCampaignResultStatistics';
export * from './abTestVersionClicksInner';
export * from './abTestVersionStats';
export * from './addChildDomain';
export * from './addContactToList';
export * from './addSubscriptionMemberPayload';
export * from './aggregateBalance';
export * from './authenticateDomainModel';
export * from './balance';
export * from './balanceDataPerContact';
export * from './balanceDefinition';
export * from './balanceDefinitionPage';
export * from './balanceLimit';
export * from './balanceOrder';
export * from './blockDomain';
export * from './cart';
export * from './companiesIdPatchRequest';
export * from './companiesImportPost200Response';
export * from './companiesImportPost400Response';
export * from './companiesLinkUnlinkIdPatchRequest';
export * from './companiesList';
export * from './companiesPost200Response';
export * from './companiesPostRequest';
export * from './company';
export * from './companyAttributesInner';
export * from './componentItems';
export * from './configuration';
export * from './contactBalancesResp';
export * from './contactErrorModel';
export * from './conversationsAgentOnlinePingPostRequest';
export * from './conversationsMessage';
export * from './conversationsMessageAttachmentsInner';
export * from './conversationsMessageFile';
export * from './conversationsMessageFileImageInfo';
export * from './conversationsMessageForwardedToSourceStatus';
export * from './conversationsMessageFrom';
export * from './conversationsMessageReplyTo';
export * from './conversationsMessageToInner';
export * from './conversationsMessagesIdPutRequest';
export * from './conversationsMessagesPostRequest';
export * from './conversationsPushedMessagesIdPutRequest';
export * from './conversationsPushedMessagesPostRequest';
export * from './conversionSourceMetrics';
export * from './conversionSourceProduct';
export * from './corporateGroupDetailsResponse';
export * from './corporateGroupDetailsResponseGroup';
export * from './corporateGroupDetailsResponseSubAccountsInner';
export * from './corporateGroupDetailsResponseUsersInner';
export * from './corporateGroupPost201Response';
export * from './corporateGroupPostRequest';
export * from './corporateGroupUnlinkGroupIdSubAccountsPutRequest';
export * from './corporateSubAccountIpAssociatePostRequest';
export * from './corporateSubAccountIpDissociatePutRequest';
export * from './corporateUserEmailPermissionsPutRequest';
export * from './corporateUserEmailPermissionsPutRequestPrivilegesInner';
export * from './corporateUserInvitationActionEmailPut200Response';
export * from './createApiKeyRequest';
export * from './createApiKeyResponse';
export * from './createAttribute';
export * from './createAttributeEnumerationInner';
export * from './createBalanceDefinitionPayload';
export * from './createBalanceLimitPayload';
export * from './createBalancePayload';
export * from './createCategoryModel';
export * from './createChild';
export * from './createContact';
export * from './createCouponCollection201Response';
export * from './createCouponCollectionRequest';
export * from './createCouponsRequest';
export * from './createDoiContact';
export * from './createDomain';
export * from './createDomainModel';
export * from './createDomainModelDnsRecords';
export * from './createDomainModelDnsRecordsDkimRecord';
export * from './createEmailCampaign';
export * from './createEmailCampaignEmailExpirationDate';
export * from './createEmailCampaignRecipients';
export * from './createEmailCampaignSender';
export * from './createExternalFeed';
export * from './createExternalFeed201Response';
export * from './createList';
export * from './createLoyaltyProgramPayload';
export * from './createModel';
export * from './createOrderPayload';
export * from './createPaymentRequest';
export * from './createPaymentResponse';
export * from './createProductModel';
export * from './createSender';
export * from './createSenderIpsInner';
export * from './createSenderModel';
export * from './createSmsCampaign';
export * from './createSmsCampaignRecipients';
export * from './createSmtpEmail';
export * from './createSmtpTemplate';
export * from './createSmtpTemplateSender';
export * from './createSubAccount';
export * from './createSubAccountResponse';
export * from './createSubscriptionPayload';
export * from './createSubscriptionResponse';
export * from './createTierGroupRequest';
export * from './createTransactionPayload';
export * from './createUpdateBatchCategory';
export * from './createUpdateBatchCategoryModel';
export * from './createUpdateBatchProducts';
export * from './createUpdateBatchProductsModel';
export * from './createUpdateCategories';
export * from './createUpdateCategory';
export * from './createUpdateContactModel';
export * from './createUpdateFolder';
export * from './createUpdateProduct';
export * from './createUpdateProducts';
export * from './createWebhook';
export * from './createWhatsAppCampaign';
export * from './createWhatsAppCampaignRecipients';
export * from './createWhatsAppTemplate';
export * from './createdBatchId';
export * from './createdProcessId';
export * from './crmAttributesPost200Response';
export * from './crmAttributesPostRequest';
export * from './crmDealsIdPatchRequest';
export * from './crmDealsLinkUnlinkIdPatchRequest';
export * from './crmDealsPost201Response';
export * from './crmDealsPostRequest';
export * from './crmTasksIdPatchRequest';
export * from './crmTasksPost201Response';
export * from './crmTasksPostRequest';
export * from './deal';
export * from './dealAttributesInner';
export * from './dealsList';
export * from './deleteHardbounces';
export * from './ecommerceAttributionMetricsConversionSourceConversionSourceIdGet200Response';
export * from './ecommerceAttributionMetricsGet200Response';
export * from './ecommerceAttributionMetricsGet200ResponseTotals';
export * from './ecommerceAttributionProductsConversionSourceConversionSourceIdGet200Response';
export * from './ecommerceConfigDisplayCurrencyGet200Response';
export * from './emailExportRecipients';
export * from './errorModel';
export * from './errorResponse';
export * from './event';
export * from './eventIdentifiers';
export * from './exportWebhooksHistory';
export * from './fetchTemplatePreview';
export * from './fileData';
export * from './fileDownloadableLink';
export * from './getAccount';
export * from './getAccountActivity';
export * from './getAccountActivityLogsInner';
export * from './getAccountAllOfMarketingAutomation';
export * from './getAccountAllOfPlan';
export * from './getAccountAllOfRelay';
export * from './getAccountAllOfRelayData';
export * from './getAggregatedReport';
export * from './getAllExternalFeeds';
export * from './getAllExternalFeedsFeedsInner';
export * from './getAttributes';
export * from './getAttributesAttributesInner';
export * from './getAttributesAttributesInnerEnumerationInner';
export * from './getBlockedDomains';
export * from './getCampaignOverview';
export * from './getCampaignRecipients';
export * from './getCampaignStats';
export * from './getCategories';
export * from './getCategoryDetails';
export * from './getChildDomain';
export * from './getClient';
export * from './getContactCampaignStats';
export * from './getContactCampaignStatsClickedInner';
export * from './getContactCampaignStatsOpenedInner';
export * from './getContactCampaignStatsTransacAttributesInner';
export * from './getContactCampaignStatsUnsubscriptions';
export * from './getContactDetails';
export * from './getContacts';
export * from './getCorporateInvitedUsersList';
export * from './getCorporateInvitedUsersListUsersInner';
export * from './getCorporateInvitedUsersListUsersInnerFeatureAccess';
export * from './getCorporateInvitedUsersListUsersInnerGroups';
export * from './getCorporateUserPermission';
export * from './getCorporateUserPermissionFeatureAccess';
export * from './getCorporateUserPermissionGroupsInner';
export * from './getCouponCollection';
export * from './getDeviceBrowserStats';
export * from './getDomainConfigurationModel';
export * from './getDomainsList';
export * from './getDomainsListDomainsInner';
export * from './getEmailCampaign';
export * from './getEmailCampaigns';
export * from './getEmailCampaignsCampaignsInner';
export * from './getEmailEventReport';
export * from './getEmailEventReportEventsInner';
export * from './getExtendedCampaignOverview';
export * from './getExtendedCampaignOverviewAllOfSender';
export * from './getExtendedCampaignStats';
export * from './getExtendedClient';
export * from './getExtendedClientAllOfAddress';
export * from './getExtendedContactDetails';
export * from './getExtendedContactDetailsAllOfStatistics';
export * from './getExtendedContactDetailsAllOfStatisticsClicked';
export * from './getExtendedContactDetailsAllOfStatisticsLinks';
export * from './getExtendedContactDetailsAllOfStatisticsMessagesSent';
export * from './getExtendedContactDetailsAllOfStatisticsOpened';
export * from './getExtendedContactDetailsAllOfStatisticsUnsubscriptions';
export * from './getExtendedContactDetailsAllOfStatisticsUnsubscriptionsAdminUnsubscription';
export * from './getExtendedContactDetailsAllOfStatisticsUnsubscriptionsUserUnsubscription';
export * from './getExtendedList';
export * from './getExtendedListAllOfCampaignStats';
export * from './getExternalFeedByUUID';
export * from './getExternalFeedByUUIDHeadersInner';
export * from './getFolder';
export * from './getFolderLists';
export * from './getFolders';
export * from './getInboundEmailEvents';
export * from './getInboundEmailEventsByUuid';
export * from './getInboundEmailEventsByUuidAttachmentsInner';
export * from './getInboundEmailEventsByUuidLogsInner';
export * from './getInboundEmailEventsEventsInner';
export * from './getInvitedUsersList';
export * from './getInvitedUsersListUsersInner';
export * from './getInvitedUsersListUsersInnerFeatureAccess';
export * from './getIp';
export * from './getIpFromSender';
export * from './getIps';
export * from './getIpsFromSender';
export * from './getList';
export * from './getLists';
export * from './getListsListsInner';
export * from './getOrders';
export * from './getOrdersOrdersInner';
export * from './getPaymentRequest';
export * from './getProcess';
export * from './getProcesses';
export * from './getProductDetails';
export * from './getProducts';
export * from './getReports';
export * from './getReportsReportsInner';
export * from './getScheduledEmailByBatchId';
export * from './getScheduledEmailByBatchIdBatchesInner';
export * from './getScheduledEmailByMessageId';
export * from './getSegment';
export * from './getSegments';
export * from './getSendersList';
export * from './getSendersListSendersInner';
export * from './getSendersListSendersInnerIpsInner';
export * from './getSharedTemplateUrl';
export * from './getSmsCampaign';
export * from './getSmsCampaignOverview';
export * from './getSmsCampaignStats';
export * from './getSmsCampaigns';
export * from './getSmsCampaignsCampaignsInner';
export * from './getSmsEventReport';
export * from './getSmsEventReportEventsInner';
export * from './getSmtpTemplateOverview';
export * from './getSmtpTemplateOverviewSender';
export * from './getSmtpTemplates';
export * from './getSsoToken';
export * from './getStatsByDevice';
export * from './getSubAccountGroups200ResponseInner';
export * from './getTransacAggregatedSmsReport';
export * from './getTransacBlockedContacts';
export * from './getTransacBlockedContactsContactsInner';
export * from './getTransacBlockedContactsContactsInnerReason';
export * from './getTransacEmailContent';
export * from './getTransacEmailContentEventsInner';
export * from './getTransacEmailsList';
export * from './getTransacEmailsListTransactionalEmailsInner';
export * from './getTransacSmsReport';
export * from './getTransacSmsReportReportsInner';
export * from './getUserPermission';
export * from './getUserPermissionPrivilegesInner';
export * from './getWATemplates';
export * from './getWATemplatesTemplatesInner';
export * from './getWebhook';
export * from './getWebhookAuth';
export * from './getWebhookHeadersInner';
export * from './getWebhooks';
export * from './getWhatsAppConfig';
export * from './getWhatsappCampaignOverview';
export * from './getWhatsappCampaigns';
export * from './getWhatsappCampaignsCampaignsInner';
export * from './getWhatsappEventReport';
export * from './getWhatsappEventReportEventsInner';
export * from './inviteAdminUser';
export * from './inviteAdminUserPrivilegesInner';
export * from './inviteuser';
export * from './inviteuserPrivilegesInner';
export * from './loyaltyProgram';
export * from './loyaltyProgramPage';
export * from './loyaltyProgramValidationError';
export * from './loyaltyTierPage';
export * from './mainAttributeRewardPayload';
export * from './mainBillingPayload';
export * from './mainCodeCountHttpResponse';
export * from './mainCreateRedeemPayload';
export * from './mainCreateRewardPayload';
export * from './mainCreateRewardResponse';
export * from './mainErrorResponse';
export * from './mainFilter';
export * from './mainGenerator';
export * from './mainGetContactRewardsPayload';
export * from './mainIdentifiersPayload';
export * from './mainLimit';
export * from './mainModelContactReward';
export * from './mainModelContactRewardsResp';
export * from './mainNodeResponse';
export * from './mainOrderPayload';
export * from './mainProduct';
export * from './mainProductPayload';
export * from './mainRedeem';
export * from './mainResultParameterResponse';
export * from './mainReward';
export * from './mainRewardAttribution';
export * from './mainRewardConfigurations';
export * from './mainRewardPage';
export * from './mainRewardPageObj';
export * from './mainRewardValidate';
export * from './mainRule';
export * from './mainRuleConditionResponse';
export * from './mainRuleEventResponse';
export * from './mainRuleResultResponse';
export * from './mainValidateRewardPayload';
export * from './mainValueResponse';
export * from './mainVoucherRevokePayload';
export * from './masterDetailsResponse';
export * from './masterDetailsResponseBillingInfo';
export * from './masterDetailsResponseBillingInfoAddress';
export * from './masterDetailsResponseBillingInfoName';
export * from './masterDetailsResponsePlanInfo';
export * from './masterDetailsResponsePlanInfoFeaturesInner';
export * from './memberContact';
export * from './modelSubscriptionBalanceResp';
export * from './note';
export * from './noteData';
export * from './noteId';
export * from './notification';
export * from './order';
export * from './orderBatch';
export * from './orderBilling';
export * from './orderIdentifiers';
export * from './orderProductsInner';
export * from './otp';
export * from './patchLoyaltyProgramPayload';
export * from './pipeline';
export * from './pipelineStage';
export * from './postContactInfo';
export * from './postContactInfoContacts';
export * from './postSendFailed';
export * from './postSendSmsTestFailed';
export * from './putRevokeUserPermission';
export * from './putresendcancelinvitation';
export * from './removeContactFromList';
export * from './requestContactExport';
export * from './requestContactExportCustomContactFilter';
export * from './requestContactImport';
export * from './requestContactImportJsonBodyInner';
export * from './requestContactImportNewList';
export * from './requestSmsRecipientExport';
export * from './scheduleSmtpEmail';
export * from './sendReport';
export * from './sendReportEmail';
export * from './sendSms';
export * from './sendSmtpEmail';
export * from './sendSmtpEmailAttachmentInner';
export * from './sendSmtpEmailBccInner';
export * from './sendSmtpEmailCcInner';
export * from './sendSmtpEmailMessageVersionsInner';
export * from './sendSmtpEmailMessageVersionsInnerReplyTo';
export * from './sendSmtpEmailMessageVersionsInnerToInner';
export * from './sendSmtpEmailReplyTo';
export * from './sendSmtpEmailSender';
export * from './sendSmtpEmailToInner';
export * from './sendTestEmail';
export * from './sendTestSms';
export * from './sendTransacSms';
export * from './sendTransacSmsTag';
export * from './sendWhatsappMessage';
export * from './sendWhatsappMessage201Response';
export * from './ssoTokenRequest';
export * from './ssoTokenRequestCorporate';
export * from './subAccountAppsToggleRequest';
export * from './subAccountDetailsResponse';
export * from './subAccountDetailsResponseGroupsInner';
export * from './subAccountDetailsResponsePlanInfo';
export * from './subAccountDetailsResponsePlanInfoCredits';
export * from './subAccountDetailsResponsePlanInfoCreditsEmails';
export * from './subAccountDetailsResponsePlanInfoCreditsExternalFeeds';
export * from './subAccountDetailsResponsePlanInfoCreditsSms';
export * from './subAccountDetailsResponsePlanInfoCreditsWhatsapp';
export * from './subAccountDetailsResponsePlanInfoCreditsWpSubscribers';
export * from './subAccountDetailsResponsePlanInfoFeatures';
export * from './subAccountDetailsResponsePlanInfoFeaturesInbox';
export * from './subAccountDetailsResponsePlanInfoFeaturesLandingPage';
export * from './subAccountDetailsResponsePlanInfoFeaturesSalesUsers';
export * from './subAccountDetailsResponsePlanInfoFeaturesUsers';
export * from './subAccountUpdatePlanRequest';
export * from './subAccountUpdatePlanRequestCredits';
export * from './subAccountUpdatePlanRequestFeatures';
export * from './subAccountsResponse';
export * from './subAccountsResponseSubAccountsInner';
export * from './subAccountsResponseSubAccountsInnerGroupsInner';
export * from './subAccountsUpdatePlanRequest';
export * from './subAccountsUpdatePlanRequestCredits';
export * from './subAccountsUpdatePlanRequestFeatures';
export * from './subscription';
export * from './subscriptionAggregateBalance';
export * from './subscriptionAttributedReward';
export * from './subscriptionBalances';
export * from './subscriptionHandlerInfo';
export * from './subscriptionMember';
export * from './subscriptionTier';
export * from './task';
export * from './taskList';
export * from './taskReminder';
export * from './taskTypes';
export * from './templatePreview';
export * from './tier';
export * from './tierAccessConditionsInner';
export * from './tierForContact';
export * from './tierGroup';
export * from './tierGroupPage';
export * from './tierRequest';
export * from './tierRequestAccessConditionsInner';
export * from './tierRequestPutPayload';
export * from './tierRequestTierRewardsInner';
export * from './tierTierRewardsInner';
export * from './transaction';
export * from './transactionHistory';
export * from './transactionHistoryResp';
export * from './unauthorizedResponse';
export * from './updateAttribute';
export * from './updateAttributeEnumerationInner';
export * from './updateBalanceDefinitionPayload';
export * from './updateBalanceLimitPayload';
export * from './updateBatchContacts';
export * from './updateBatchContactsContactsInner';
export * from './updateBatchContactsModel';
export * from './updateCampaignStatus';
export * from './updateChild';
export * from './updateContact';
export * from './updateCouponCollection200Response';
export * from './updateCouponCollectionRequest';
export * from './updateEmailCampaign';
export * from './updateEmailCampaignEmailExpirationDate';
export * from './updateEmailCampaignRecipients';
export * from './updateEmailCampaignSender';
export * from './updateExternalFeed';
export * from './updateList';
export * from './updateLoyaltyProgramPayload';
export * from './updateSender';
export * from './updateSmsCampaign';
export * from './updateSmtpTemplate';
export * from './updateSmtpTemplateSender';
export * from './updateTierGroupRequest';
export * from './updateWebhook';
export * from './updateWhatsAppCampaign';
export * from './uploadImageModel';
export * from './uploadImageToGallery';
export * from './variablesItems';
export * from './whatsappCampStats';
export * from './whatsappCampTemplate';
import * as fs from 'fs';
export interface RequestDetailedFile {
    value: Buffer;
    options?: {
        filename?: string;
        contentType?: string;
    };
}
export type RequestFile = string | Buffer | fs.ReadStream | RequestDetailedFile;
export declare class ObjectSerializer {
    static findCorrectType(data: any, expectedType: string): any;
    static serialize(data: any, type: string): any;
    static deserialize(data: any, type: string): any;
}
export interface Authentication {
    applyToRequest(requestOptions: localVarRequest.Options): Promise<void> | void;
}
export declare class HttpBasicAuth implements Authentication {
    username: string;
    password: string;
    applyToRequest(requestOptions: localVarRequest.Options): void;
}
export declare class HttpBearerAuth implements Authentication {
    accessToken: string | (() => string);
    applyToRequest(requestOptions: localVarRequest.Options): void;
}
export declare class ApiKeyAuth implements Authentication {
    private location;
    private paramName;
    apiKey: string;
    constructor(location: string, paramName: string);
    applyToRequest(requestOptions: localVarRequest.Options): void;
}
export declare class OAuth implements Authentication {
    accessToken: string;
    applyToRequest(requestOptions: localVarRequest.Options): void;
}
export declare class VoidAuth implements Authentication {
    username: string;
    password: string;
    applyToRequest(_: localVarRequest.Options): void;
}
export type Interceptor = (requestOptions: localVarRequest.Options) => (Promise<void> | void);
