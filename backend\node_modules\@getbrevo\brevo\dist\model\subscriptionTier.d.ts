export declare class SubscriptionTier {
    'contactId'?: number;
    'createdAt'?: string;
    'groupId'?: string;
    'loyaltyProgramId'?: string;
    'meta'?: {
        [key: string]: object;
    };
    'tierId'?: string;
    'updatedAt'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
