import { SubAccountsResponseSubAccountsInnerGroupsInner } from './subAccountsResponseSubAccountsInnerGroupsInner';
export declare class SubAccountsResponseSubAccountsInner {
    'id': number;
    'companyName': string;
    'active': boolean;
    'createdAt': number;
    'groups': Array<SubAccountsResponseSubAccountsInnerGroupsInner>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
