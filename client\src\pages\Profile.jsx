import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  PencilIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  StarIcon,
  CalendarIcon,
  CogIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { updateProfile } from '../store/slices/authSlice';
import ProfilePicture from '../components/profile/ProfilePicture';
import userService from '../services/userService';

const Profile = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('personal');
  const [userStats, setUserStats] = useState({
    totalOrders: 0,
    totalSpent: 0,
    averageRating: 0,
    memberSince: null
  });
  
  const { register, handleSubmit, formState: { errors }, reset } = useForm({
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || '',
      dateOfBirth: user?.dateOfBirth || '',
      preferences: {
        notifications: user?.preferences?.notifications || true,
        newsletter: user?.preferences?.newsletter || false,
        smsUpdates: user?.preferences?.smsUpdates || true
      }
    }
  });

  // Load user statistics
  useEffect(() => {
    const loadUserStats = async () => {
      try {
        const response = await userService.getUserStats();
        setUserStats({
          totalOrders: response.data.totalOrders || 0,
          totalSpent: response.data.totalSpent || 0,
          averageRating: response.data.averageRating || 0,
          memberSince: response.data.memberSince ? new Date(response.data.memberSince) : new Date()
        });
      } catch (error) {
        console.error('Failed to load user stats:', error);
        // Set default values on error
        setUserStats({
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 0,
          memberSince: new Date()
        });
      }
    };

    if (user) {
      loadUserStats();
    }
  }, [user]);

  const onSubmitProfile = async (data) => {
    try {
      const response = await userService.updateProfile(data);
      dispatch(updateProfile(response.data.user));
      toast.success(t('profileUpdatedSuccess', 'Profile updated successfully!'));
      setIsEditing(false);
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error(t('profileUpdateFailed', 'Failed to update profile'));
    }
  };

  const handleProfilePictureUpdate = (imageUrl) => {
    dispatch(updateProfile({ ...user, profilePicture: imageUrl }));
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    reset({
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || ''
    });
  };

  const tabs = [
    { id: 'personal', name: t('personalInfo', 'Personal Information'), icon: UserIcon },
    { id: 'preferences', name: t('preferences', 'Preferences'), icon: CogIcon },
    { id: 'security', name: t('security', 'Security'), icon: ShieldCheckIcon }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('profile')}
            </h1>
            <p className="text-gray-600 mt-1">
              {t('manageProfileSettings', 'Manage your account settings and preferences')}
            </p>
          </div>
          {!isEditing && activeTab === 'personal' && (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              {t('editProfile', 'Edit Profile')}
            </button>
          )}
        </div>

        {/* Profile Header Card */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <ProfilePicture
              user={user}
              onImageUpdate={handleProfilePictureUpdate}
              size="xl"
              editable={true}
            />
            <div className="text-center sm:text-left">
              <h2 className="text-2xl font-bold text-gray-900">{user?.name || t('unnamed', 'Unnamed User')}</h2>
              <p className="text-gray-600">{user?.email}</p>
              <div className="flex items-center justify-center sm:justify-start mt-2 space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-1" />
                  {t('memberSince', 'Member since')} {userStats.memberSince?.toLocaleDateString() || 'N/A'}
                </div>
                <div className="flex items-center">
                  <ShoppingBagIcon className="h-4 w-4 mr-1" />
                  {userStats.totalOrders} {t('orders', 'orders')}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'border-orange-500 text-orange-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <IconComponent className="h-5 w-5 mr-2" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {/* Personal Information Tab */}
            {activeTab === 'personal' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {t('personalInformation', 'Personal Information')}
                </h2>

              {isEditing ? (
                <form onSubmit={handleSubmit(onSubmitProfile)} className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('name')} *
                    </label>
                    <div className="relative">
                      <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        {...register('name', { required: 'Name is required' })}
                        type="text"
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    </div>
                    {errors.name && (
                      <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('email')} *
                    </label>
                    <div className="relative">
                      <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        {...register('email', { 
                          required: 'Email is required',
                          pattern: {
                            value: /^\S+@\S+$/i,
                            message: 'Please enter a valid email'
                          }
                        })}
                        type="email"
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    </div>
                    {errors.email && (
                      <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('phoneNumber')} *
                    </label>
                    <div className="relative">
                      <PhoneIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        {...register('phone', { required: 'Phone number is required' })}
                        type="tel"
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    </div>
                    {errors.phone && (
                      <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('dateOfBirth', 'Date of Birth')}
                    </label>
                    <div className="relative">
                      <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        {...register('dateOfBirth')}
                        type="date"
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('address', 'Address')}
                    </label>
                    <div className="relative">
                      <MapPinIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <textarea
                        {...register('address')}
                        rows={3}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        placeholder={t('enterAddress', 'Enter your address')}
                      />
                    </div>
                  </div>

                  <div className="flex space-x-4">
                    <motion.button
                      type="submit"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
                    >
                      {t('saveChanges', 'Save Changes')}
                    </motion.button>
                    <button
                      type="button"
                      onClick={handleCancelEdit}
                      className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                    >
                      {t('cancel', 'Cancel')}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">{t('name', 'Name')}</p>
                      <p className="font-medium text-gray-900">{user?.name || t('notProvided', 'Not provided')}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">{t('email', 'Email')}</p>
                      <p className="font-medium text-gray-900">{user?.email || t('notProvided', 'Not provided')}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <PhoneIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">{t('phone', 'Phone')}</p>
                      <p className="font-medium text-gray-900">{user?.phone || t('notProvided', 'Not provided')}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">{t('dateOfBirth', 'Date of Birth')}</p>
                      <p className="font-medium text-gray-900">
                        {user?.dateOfBirth ? new Date(user.dateOfBirth).toLocaleDateString() : t('notProvided', 'Not provided')}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <MapPinIcon className="h-5 w-5 text-gray-400 mt-1" />
                    <div>
                      <p className="text-sm text-gray-500">{t('address', 'Address')}</p>
                      <p className="font-medium text-gray-900">{user?.address || t('notProvided', 'Not provided')}</p>
                    </div>
                  </div>
                </div>
              )}
              </div>
            )}

            {/* Preferences Tab */}
            {activeTab === 'preferences' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {t('preferences', 'Preferences')}
                </h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      {t('notifications', 'Notifications')}
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{t('emailNotifications', 'Email Notifications')}</p>
                          <p className="text-sm text-gray-500">{t('receiveOrderUpdates', 'Receive order updates and promotions')}</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            {...register('preferences.notifications')}
                            type="checkbox"
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{t('smsUpdates', 'SMS Updates')}</p>
                          <p className="text-sm text-gray-500">{t('receiveDeliveryUpdates', 'Receive delivery status updates')}</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            {...register('preferences.smsUpdates')}
                            type="checkbox"
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{t('newsletter', 'Newsletter')}</p>
                          <p className="text-sm text-gray-500">{t('receiveWeeklyNewsletter', 'Receive weekly menu updates and special offers')}</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            {...register('preferences.newsletter')}
                            type="checkbox"
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="pt-6 border-t border-gray-200">
                    <button
                      onClick={handleSubmit(onSubmitProfile)}
                      className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
                    >
                      {t('savePreferences', 'Save Preferences')}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {t('security', 'Security')}
                </h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      {t('changePassword', 'Change Password')}
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('currentPassword', 'Current Password')}
                        </label>
                        <input
                          type="password"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('newPassword', 'New Password')}
                        </label>
                        <input
                          type="password"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {t('confirmPassword', 'Confirm New Password')}
                        </label>
                        <input
                          type="password"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                        />
                      </div>
                      <button className="px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium">
                        {t('updatePassword', 'Update Password')}
                      </button>
                    </div>
                  </div>

                  <div className="pt-6 border-t border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      {t('accountActions', 'Account Actions')}
                    </h3>
                    <div className="space-y-3">
                      <button className="w-full text-left px-4 py-3 border border-red-200 rounded-lg hover:bg-red-50 transition-colors">
                        <p className="font-medium text-red-600">{t('deleteAccount', 'Delete Account')}</p>
                        <p className="text-sm text-red-500">{t('deleteAccountWarning', 'Permanently delete your account and all data')}</p>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                {t('accountSummary', 'Account Summary')}
              </h2>

              <div className="space-y-4">
                <motion.div
                  className="text-center p-4 bg-orange-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center justify-center mb-2">
                    <ShoppingBagIcon className="h-6 w-6 text-orange-600 mr-2" />
                    <p className="text-2xl font-bold text-orange-600">{userStats.totalOrders}</p>
                  </div>
                  <p className="text-sm text-gray-600">{t('totalOrders', 'Total Orders')}</p>
                </motion.div>

                <motion.div
                  className="text-center p-4 bg-green-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center justify-center mb-2">
                    <CurrencyDollarIcon className="h-6 w-6 text-green-600 mr-2" />
                    <p className="text-2xl font-bold text-green-600">{userStats.totalSpent.toLocaleString()}</p>
                  </div>
                  <p className="text-sm text-gray-600">{t('totalSpent', 'Total Spent')} (XAF)</p>
                </motion.div>

                <motion.div
                  className="text-center p-4 bg-blue-50 rounded-lg"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center justify-center mb-2">
                    <StarIcon className="h-6 w-6 text-blue-600 mr-2" />
                    <p className="text-2xl font-bold text-blue-600">⭐ {userStats.averageRating}</p>
                  </div>
                  <p className="text-sm text-gray-600">{t('averageRating', 'Average Rating Given')}</p>
                </motion.div>
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center mb-2">
                  <CalendarIcon className="h-4 w-4 text-gray-500 mr-2" />
                  <p className="text-sm text-gray-500">{t('memberSince', 'Member since')}</p>
                </div>
                <p className="font-medium text-gray-900">
                  {userStats.memberSince?.toLocaleDateString() || 'N/A'}
                </p>
              </div>

              {/* Quick Actions */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">{t('quickActions', 'Quick Actions')}</h3>
                <div className="space-y-2">
                  <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                    {t('viewOrderHistory', 'View Order History')}
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                    {t('manageFavorites', 'Manage Favorites')}
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                    {t('downloadData', 'Download My Data')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
