import express from 'express';
import { body, validationResult } from 'express-validator';
import SpecialOrder from '../models/SpecialOrder.js';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.js';
import emailService from '../services/emailService.js';

const router = express.Router();

// Validation rules
const specialOrderValidation = [
  body('type')
    .isIn(['event', 'bulk'])
    .withMessage('Type must be event or bulk'),
  body('customer.name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Customer name is required'),
  body('customer.phone')
    .matches(/^\+?[1-9]\d{1,14}$/)
    .withMessage('Valid phone number is required'),
  body('deliveryLocation.address')
    .notEmpty()
    .withMessage('Delivery address is required')
];

// @route   POST /api/special-orders
// @desc    Submit special order
// @access  Public
router.post('/', optionalAuth, specialOrderValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const specialOrder = new SpecialOrder(req.body);
    await specialOrder.save();

    // Send email notification to owner
    try {
      await emailService.sendSpecialOrderNotification({
        customerName: specialOrder.customer.name,
        customerPhone: specialOrder.customer.phone,
        customerEmail: specialOrder.customer.email,
        orderType: specialOrder.type,
        details: specialOrder.details,
        eventDate: specialOrder.eventDetails?.date,
        numberOfPeople: specialOrder.eventDetails?.numberOfPeople,
        notes: specialOrder.notes
      });
    } catch (emailError) {
      console.error('Failed to send special order notification email:', emailError);
      // Don't fail the order submission if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Special order submitted successfully',
      data: {
        orderNumber: specialOrder.orderNumber,
        estimatedResponse: '24 hours'
      }
    });
  } catch (error) {
    console.error('Submit special order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit special order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/special-orders
// @desc    Get special orders
// @access  Private (Owner only)
router.get('/', authenticate, authorize('owner'), async (req, res) => {
  try {
    const { status, type, page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const query = {};
    if (status) query.status = status;
    if (type) query.type = type;

    const specialOrders = await SpecialOrder.find(query)
      .populate('assignedTo', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await SpecialOrder.countDocuments(query);

    res.json({
      success: true,
      data: {
        specialOrders,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / parseInt(limit)),
          totalItems: total,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get special orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get special orders',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/special-orders/:id
// @desc    Get single special order
// @access  Private (Owner only)
router.get('/:id', authenticate, authorize('owner'), async (req, res) => {
  try {
    const specialOrder = await SpecialOrder.findById(req.params.id)
      .populate([
        { path: 'selectedItems.menuItem', select: 'name price' },
        { path: 'assignedTo', select: 'name email phone' },
        { path: 'statusHistory.updatedBy', select: 'name role' },
        { path: 'communicationLog.handledBy', select: 'name' }
      ]);

    if (!specialOrder) {
      return res.status(404).json({
        success: false,
        message: 'Special order not found'
      });
    }

    res.json({
      success: true,
      data: { specialOrder }
    });
  } catch (error) {
    console.error('Get special order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get special order',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PATCH /api/special-orders/:id/status
// @desc    Update special order status
// @access  Private (Owner only)
router.patch('/:id/status', authenticate, authorize('owner'), [
  body('status')
    .isIn(['submitted', 'reviewed', 'quoted', 'confirmed', 'in-preparation', 'ready', 'delivered', 'cancelled', 'completed'])
    .withMessage('Invalid status'),
  body('notes').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, notes = '' } = req.body;

    const specialOrder = await SpecialOrder.findById(req.params.id);

    if (!specialOrder) {
      return res.status(404).json({
        success: false,
        message: 'Special order not found'
      });
    }

    await specialOrder.updateStatus(status, req.user.id, notes);

    res.json({
      success: true,
      message: 'Special order status updated successfully',
      data: { specialOrder }
    });
  } catch (error) {
    console.error('Update special order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update special order status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
