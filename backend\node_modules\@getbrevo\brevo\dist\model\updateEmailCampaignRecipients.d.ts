export declare class UpdateEmailCampaignRecipients {
    'exclusionListIds'?: Array<number>;
    'listIds'?: Array<number>;
    'segmentIds'?: Array<number>;
    'exclusionSegmentIds'?: Array<number>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
