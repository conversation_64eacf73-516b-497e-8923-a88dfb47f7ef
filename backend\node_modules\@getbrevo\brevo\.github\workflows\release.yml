name: Node.js Package
on:
  release:
    types: [created]
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v2
      # Setup .npmrc file to publish to npm
      - uses: actions/setup-node@v2
        with:
          node-version: '14'
          registry-url: 'https://registry.npmjs.org'
      - run: npm install
      # - run: npm publish --access public
      #   env:
      #     NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      # # Setup .npmrc file to publish to GitHub Packages
      # - uses: actions/setup-node@v2
      #   with:
      #     registry-url: 'https://npm.pkg.github.com'
      #     scope: '@getbrevo'
      # # Publish to GitHub Packages
      # - run: npm publish
      #   env:
      #     NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
