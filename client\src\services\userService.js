import api from './api.js';

/**
 * User Service - Handles all user-related API calls
 */
class UserService {
  /**
   * Get current user profile
   */
  async getProfile() {
    try {
      const response = await api.get('/users/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData) {
    try {
      const response = await api.put('/users/profile', profileData);
      return response.data;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(imageFile) {
    try {
      const formData = new FormData();
      formData.append('profilePicture', imageFile);
      
      const response = await api.post('/users/profile/picture', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    }
  }

  /**
   * Remove profile picture
   */
  async removeProfilePicture() {
    try {
      const response = await api.delete('/users/profile/picture');
      return response.data;
    } catch (error) {
      console.error('Error removing profile picture:', error);
      throw error;
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await api.put('/users/change-password', {
        currentPassword,
        newPassword
      });
      return response.data;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }

  /**
   * Update notification preferences
   */
  async updatePreferences(preferences) {
    try {
      const response = await api.put('/users/preferences', preferences);
      return response.data;
    } catch (error) {
      console.error('Error updating preferences:', error);
      throw error;
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats() {
    try {
      const response = await api.get('/users/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }

  /**
   * Get user's favorite items
   */
  async getFavorites() {
    try {
      const response = await api.get('/users/favorites');
      return response.data;
    } catch (error) {
      console.error('Error fetching favorites:', error);
      throw error;
    }
  }

  /**
   * Add item to favorites
   */
  async addToFavorites(itemId) {
    try {
      const response = await api.post('/users/favorites', { itemId });
      return response.data;
    } catch (error) {
      console.error('Error adding to favorites:', error);
      throw error;
    }
  }

  /**
   * Remove item from favorites
   */
  async removeFromFavorites(itemId) {
    try {
      const response = await api.delete(`/users/favorites/${itemId}`);
      return response.data;
    } catch (error) {
      console.error('Error removing from favorites:', error);
      throw error;
    }
  }

  /**
   * Get user's addresses
   */
  async getAddresses() {
    try {
      const response = await api.get('/users/addresses');
      return response.data;
    } catch (error) {
      console.error('Error fetching addresses:', error);
      throw error;
    }
  }

  /**
   * Add new address
   */
  async addAddress(addressData) {
    try {
      const response = await api.post('/users/addresses', addressData);
      return response.data;
    } catch (error) {
      console.error('Error adding address:', error);
      throw error;
    }
  }

  /**
   * Update address
   */
  async updateAddress(addressId, addressData) {
    try {
      const response = await api.put(`/users/addresses/${addressId}`, addressData);
      return response.data;
    } catch (error) {
      console.error('Error updating address:', error);
      throw error;
    }
  }

  /**
   * Delete address
   */
  async deleteAddress(addressId) {
    try {
      const response = await api.delete(`/users/addresses/${addressId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting address:', error);
      throw error;
    }
  }

  /**
   * Set default address
   */
  async setDefaultAddress(addressId) {
    try {
      const response = await api.put(`/users/addresses/${addressId}/default`);
      return response.data;
    } catch (error) {
      console.error('Error setting default address:', error);
      throw error;
    }
  }

  /**
   * Delete user account
   */
  async deleteAccount(password) {
    try {
      const response = await api.delete('/users/account', {
        data: { password }
      });
      return response.data;
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  }

  /**
   * Export user data
   */
  async exportData() {
    try {
      const response = await api.get('/users/export-data');
      return response.data;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  /**
   * Get user activity log
   */
  async getActivityLog(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.action) params.append('action', filters.action);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      
      const queryString = params.toString();
      const url = queryString ? `/users/activity?${queryString}` : '/users/activity';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching activity log:', error);
      throw error;
    }
  }

  /**
   * Verify email address
   */
  async verifyEmail(token) {
    try {
      const response = await api.post('/users/verify-email', { token });
      return response.data;
    } catch (error) {
      console.error('Error verifying email:', error);
      throw error;
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification() {
    try {
      const response = await api.post('/users/resend-verification');
      return response.data;
    } catch (error) {
      console.error('Error resending verification:', error);
      throw error;
    }
  }

  /**
   * Update phone number
   */
  async updatePhoneNumber(phoneNumber, verificationCode = null) {
    try {
      const response = await api.put('/users/phone', { phoneNumber, verificationCode });
      return response.data;
    } catch (error) {
      console.error('Error updating phone number:', error);
      throw error;
    }
  }

  /**
   * Send phone verification code
   */
  async sendPhoneVerification(phoneNumber) {
    try {
      const response = await api.post('/users/send-phone-verification', { phoneNumber });
      return response.data;
    } catch (error) {
      console.error('Error sending phone verification:', error);
      throw error;
    }
  }

  /**
   * Get all users (admin only)
   */
  async getAllUsers(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.role) params.append('role', filters.role);
      if (filters.isActive) params.append('isActive', filters.isActive);
      if (filters.search) params.append('search', filters.search);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);
      
      const queryString = params.toString();
      const url = queryString ? `/users?${queryString}` : '/users';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching all users:', error);
      throw error;
    }
  }

  /**
   * Update user role (admin only)
   */
  async updateUserRole(userId, role) {
    try {
      const response = await api.put(`/users/${userId}/role`, { role });
      return response.data;
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  }

  /**
   * Deactivate/activate user (admin only)
   */
  async toggleUserStatus(userId, isActive) {
    try {
      const response = await api.put(`/users/${userId}/status`, { isActive });
      return response.data;
    } catch (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
const userService = new UserService();
export default userService;
