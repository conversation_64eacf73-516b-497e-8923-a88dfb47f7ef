import { MainRewardPageObj } from './mainRewardPageObj';
export declare class MainRewardPage {
    'items'?: Array<MainRewardPageObj>;
    'totalCount'?: number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
