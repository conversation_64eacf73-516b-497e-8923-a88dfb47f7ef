import { MainValueResponse } from './mainValueResponse';
export declare class MainResultParameterResponse {
    'name'?: string;
    'value'?: MainValueResponse;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
