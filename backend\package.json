{"name": "zina-chop-house-backend", "version": "1.0.0", "description": "Backend API for Zina Chop House food ordering system", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed:owner": "node scripts/seedOwner.js", "seed:delivery-zones": "node scripts/seedDeliveryZones.js", "seed:menu": "node scripts/seedMenuItems.js", "seed:real-menu": "node scripts/seedRealMenuItems.js", "seed:users": "node scripts/seedAllUsers.js", "seed:orders": "node scripts/seedSampleOrders.js", "seed": "node scripts/seedDatabase.js", "seed:all": "npm run seed:delivery-zones && npm run seed"}, "keywords": ["food", "ordering", "restaurant", "api"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"@getbrevo/brevo": "^2.5.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "sharp": "^0.33.1", "socket.io": "^4.7.4"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}