import mongoose from 'mongoose';
import dotenv from 'dotenv';
import MenuItem from '../models/MenuItem.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zina-chop-house');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Real Menu Items from MenuManagement (with proper asset images)
const menuItems = [
  {
    name: 'Jollof Rice',
    description: 'Traditional West African rice dish cooked in a flavorful tomato-based sauce with spices',
    price: 2500,
    category: 'main-course',
    images: [{ url: '/images/jollof-rice.jpg', alt: 'Jollof Rice' }],
    isAvailable: true,
    preparationTime: 30,
    ingredients: ['Rice', 'Tomatoes', 'Onions', 'Spices'],
    nutritionalInfo: { calories: 350, protein: 8, carbs: 70, fat: 5, fiber: 3 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Fried Rice',
    description: 'Delicious fried rice with mixed vegetables and your choice of protein',
    price: 2200,
    category: 'main-course',
    images: [{ url: '/images/fried-rice.jpg', alt: 'Fried Rice' }],
    isAvailable: true,
    preparationTime: 20,
    ingredients: ['Rice', 'Vegetables', 'Eggs', 'Soy Sauce'],
    nutritionalInfo: { calories: 320, protein: 12, carbs: 55, fat: 8, fiber: 2 },
    spiceLevel: 'Mild',
    isFeatured: false
  },
  {
    name: 'Ndole',
    description: 'Traditional Cameroonian dish with ndole leaves, groundnuts, and assorted meat',
    price: 3500,
    category: 'main-course',
    images: [{ url: '/images/ndole.jpg', alt: 'Ndole' }],
    isAvailable: true,
    preparationTime: 45,
    ingredients: ['Ndole leaves', 'Groundnuts', 'Meat', 'Fish'],
    nutritionalInfo: { calories: 480, protein: 25, carbs: 20, fat: 35, fiber: 8 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Eru',
    description: 'Traditional Cameroonian vegetable soup with eru leaves and assorted meat',
    price: 3200,
    category: 'main-course',
    images: [{ url: '/images/eru.jpg', alt: 'Eru' }],
    isAvailable: true,
    preparationTime: 40,
    ingredients: ['Eru leaves', 'Palm oil', 'Meat', 'Fish'],
    nutritionalInfo: { calories: 420, protein: 22, carbs: 15, fat: 30, fiber: 10 },
    spiceLevel: 'Medium',
    isFeatured: false
  },
  {
    name: 'Grilled Fish',
    description: 'Fresh fish grilled to perfection with local spices and herbs',
    price: 4000,
    category: 'main-course',
    images: [{ url: '/images/grilled-fish.jpg', alt: 'Grilled Fish' }],
    isAvailable: true,
    preparationTime: 25,
    ingredients: ['Fresh fish', 'Spices', 'Herbs'],
    nutritionalInfo: { calories: 280, protein: 35, carbs: 2, fat: 15, fiber: 0 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Pepper Soup',
    description: 'Spicy traditional soup with assorted meat and local spices',
    price: 2800,
    category: 'main-course',
    images: [{ url: '/images/pepper-soup.jpg', alt: 'Pepper Soup' }],
    isAvailable: true,
    preparationTime: 35,
    ingredients: ['Meat', 'Pepper soup spice', 'Vegetables'],
    nutritionalInfo: { calories: 250, protein: 20, carbs: 8, fat: 15, fiber: 2 },
    spiceLevel: 'Hot',
    isFeatured: false
  },
  {
    name: 'Plantain',
    description: 'Sweet fried plantains, perfectly caramelized',
    price: 1000,
    category: 'side-dish',
    images: [{ url: '/images/plantain.jpg', alt: 'Fried Plantain' }],
    isAvailable: true,
    preparationTime: 10,
    ingredients: ['Ripe plantains', 'Oil'],
    nutritionalInfo: { calories: 180, protein: 2, carbs: 40, fat: 6, fiber: 3 },
    spiceLevel: 'None',
    isFeatured: false
  },
  {
    name: 'Beans',
    description: 'Nutritious cooked beans with palm oil and spices',
    price: 1500,
    category: 'side-dish',
    images: [{ url: '/images/beans.jpg', alt: 'Beans' }],
    isAvailable: true,
    preparationTime: 25,
    ingredients: ['Black-eyed beans', 'Palm oil', 'Spices'],
    nutritionalInfo: { calories: 220, protein: 15, carbs: 35, fat: 5, fiber: 12 },
    spiceLevel: 'Mild',
    isFeatured: false
  }
];

const seedMenuItems = async () => {
    price: 2500,
    category: 'main-course',
    images: [{ url: '/images/coconut-rice.jpg', alt: 'Coconut Rice' }],
    isAvailable: true,
    preparationTime: 18,
    ingredients: ['Rice', 'Coconut Milk', 'Spices', 'Herbs'],
    nutritionalInfo: { calories: 380, protein: 6, carbs: 55, fat: 18, fiber: 2 },
    spiceLevel: 'Mild'
  },

  // TRADITIONAL DISHES
  {
    name: 'Ndole',
    description: 'Traditional Cameroonian dish with ndole leaves, groundnuts, and assorted meat',
    price: 4000,
    category: 'main-course',
    images: [{ url: '/images/ndole.jpg', alt: 'Ndole' }],
    isAvailable: true,
    preparationTime: 40,
    ingredients: ['Ndole Leaves', 'Groundnuts', 'Beef', 'Fish', 'Crayfish', 'Palm Oil'],
    nutritionalInfo: { calories: 520, protein: 32, carbs: 28, fat: 35, fiber: 8 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Water Fufu and Eru',
    description: 'Traditional water fufu served with eru vegetables and assorted meat',
    price: 3500,
    category: 'main-course',
    images: [{ url: '/images/fufu-eru.jpg', alt: 'Water Fufu and Eru' }],
    isAvailable: true,
    preparationTime: 35,
    ingredients: ['Cassava', 'Eru Leaves', 'Palm Oil', 'Meat', 'Fish', 'Crayfish'],
    nutritionalInfo: { calories: 420, protein: 25, carbs: 48, fat: 18, fiber: 10 },
    spiceLevel: 'Medium'
  },
  {
    name: 'Achu and Yellow Soup',
    description: 'Traditional achu (cocoyam fufu) with rich yellow soup',
    price: 3800,
    category: 'main-course',
    images: [{ url: '/images/achu-yellow-soup.jpg', alt: 'Achu and Yellow Soup' }],
    isAvailable: true,
    preparationTime: 45,
    ingredients: ['Cocoyam', 'Palm Oil', 'Spices', 'Meat', 'Fish'],
    nutritionalInfo: { calories: 480, protein: 28, carbs: 42, fat: 25, fiber: 6 },
    spiceLevel: 'Medium'
  },

  // GRILLED ITEMS
  {
    name: 'Grilled Tilapia',
    description: 'Fresh tilapia grilled to perfection with local spices and herbs',
    price: 4500,
    category: 'main-course',
    images: [{ url: '/images/grilled-tilapia.jpg', alt: 'Grilled Tilapia' }],
    isAvailable: true,
    preparationTime: 25,
    ingredients: ['Fresh Tilapia', 'Local Spices', 'Herbs', 'Lemon', 'Garlic'],
    nutritionalInfo: { calories: 350, protein: 40, carbs: 3, fat: 20, fiber: 0 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Suya Platter',
    description: 'Spicy grilled beef skewers with traditional suya spices',
    price: 2500,
    category: 'main-course',
    images: [{ url: '/images/suya.jpg', alt: 'Suya Platter' }],
    isAvailable: true,
    preparationTime: 20,
    ingredients: ['Beef', 'Suya Spice', 'Onions', 'Tomatoes'],
    nutritionalInfo: { calories: 320, protein: 35, carbs: 8, fat: 18, fiber: 2 },
    spiceLevel: 'Hot'
  },
  {
    name: 'Grilled Chicken',
    description: 'Tender grilled chicken marinated in aromatic spices',
    price: 3500,
    category: 'main-course',
    images: [{ url: '/images/grilled-chicken.jpg', alt: 'Grilled Chicken' }],
    isAvailable: true,
    preparationTime: 30,
    ingredients: ['Chicken', 'Marinade', 'Spices', 'Herbs'],
    nutritionalInfo: { calories: 380, protein: 42, carbs: 5, fat: 22, fiber: 1 },
    spiceLevel: 'Medium'
  },

  // SOUPS
  {
    name: 'Pepper Soup',
    description: 'Spicy traditional pepper soup with assorted meat',
    price: 2800,
    category: 'main-course',
    images: [{ url: '/images/pepper-soup.jpg', alt: 'Pepper Soup' }],
    isAvailable: true,
    preparationTime: 35,
    ingredients: ['Assorted Meat', 'Pepper Soup Spice', 'Vegetables', 'Herbs'],
    nutritionalInfo: { calories: 280, protein: 25, carbs: 12, fat: 16, fiber: 3 },
    spiceLevel: 'Hot'
  },
  {
    name: 'Fish Soup',
    description: 'Rich fish soup with vegetables and traditional spices',
    price: 3200,
    category: 'main-course',
    images: [{ url: '/images/fish-soup.jpg', alt: 'Fish Soup' }],
    isAvailable: true,
    preparationTime: 30,
    ingredients: ['Fresh Fish', 'Vegetables', 'Spices', 'Herbs'],
    nutritionalInfo: { calories: 250, protein: 28, carbs: 15, fat: 12, fiber: 4 },
    spiceLevel: 'Medium'
  },

  // SIDES
  {
    name: 'Fried Plantains',
    description: 'Sweet fried plantains, perfectly caramelized',
    price: 1500,
    category: 'side-dish',
    images: [{ url: '/images/fried-plantains.jpg', alt: 'Fried Plantains' }],
    isAvailable: true,
    preparationTime: 10,
    ingredients: ['Ripe Plantains', 'Oil', 'Salt'],
    nutritionalInfo: { calories: 220, protein: 2, carbs: 45, fat: 8, fiber: 4 },
    spiceLevel: 'None'
  },
  {
    name: 'Boiled Yam',
    description: 'Fresh yam boiled to perfection',
    price: 1200,
    category: 'side-dish',
    images: [{ url: '/images/boiled-yam.jpg', alt: 'Boiled Yam' }],
    isAvailable: true,
    preparationTime: 15,
    ingredients: ['Fresh Yam', 'Salt'],
    nutritionalInfo: { calories: 180, protein: 4, carbs: 42, fat: 1, fiber: 6 },
    spiceLevel: 'None'
  },
  {
    name: 'Beans and Plantain',
    description: 'Nutritious beans served with fried plantains',
    price: 2000,
    category: 'side-dish',
    images: [{ url: '/images/beans-plantain.jpg', alt: 'Beans and Plantain' }],
    isAvailable: true,
    preparationTime: 25,
    ingredients: ['Black-eyed Beans', 'Plantains', 'Palm Oil', 'Spices'],
    nutritionalInfo: { calories: 320, protein: 12, carbs: 55, fat: 8, fiber: 12 },
    spiceLevel: 'Mild'
  },

  // BEVERAGES
  {
    name: 'Fresh Palm Wine',
    description: 'Traditional fresh palm wine',
    price: 800,
    category: 'beverage',
    images: [{ url: '/images/palm-wine.jpg', alt: 'Fresh Palm Wine' }],
    isAvailable: true,
    preparationTime: 2,
    ingredients: ['Palm Wine'],
    nutritionalInfo: { calories: 120, protein: 1, carbs: 15, fat: 0, fiber: 0 },
    spiceLevel: 'None'
  },
  {
    name: 'Zobo Drink',
    description: 'Refreshing hibiscus drink with natural flavors',
    price: 600,
    category: 'beverage',
    images: [{ url: '/images/zobo.jpg', alt: 'Zobo Drink' }],
    isAvailable: true,
    preparationTime: 5,
    ingredients: ['Hibiscus Leaves', 'Ginger', 'Cucumber', 'Pineapple'],
    nutritionalInfo: { calories: 45, protein: 0, carbs: 12, fat: 0, fiber: 1 },
    spiceLevel: 'None'
  },
  {
    name: 'Fresh Fruit Juice',
    description: 'Freshly squeezed seasonal fruit juice',
    price: 700,
    category: 'beverage',
    images: [{ url: '/images/fruit-juice.jpg', alt: 'Fresh Fruit Juice' }],
    isAvailable: true,
    preparationTime: 5,
    ingredients: ['Seasonal Fruits'],
    nutritionalInfo: { calories: 80, protein: 1, carbs: 20, fat: 0, fiber: 2 },
    spiceLevel: 'None'
  },

  // SNACKS
  {
    name: 'Akara',
    description: 'Deep-fried bean cakes, crispy outside and soft inside',
    price: 1000,
    category: 'side-dish',
    images: [{ url: '/images/akara.jpg', alt: 'Akara' }],
    isAvailable: true,
    preparationTime: 15,
    ingredients: ['Black-eyed Beans', 'Onions', 'Pepper', 'Oil'],
    nutritionalInfo: { calories: 180, protein: 8, carbs: 15, fat: 12, fiber: 5 },
    spiceLevel: 'Mild'
  },
  {
    name: 'Moi Moi',
    description: 'Steamed bean pudding with eggs and vegetables',
    price: 1500,
    category: 'side-dish',
    images: [{ url: '/images/moimoi.jpg', alt: 'Moi Moi' }],
    isAvailable: true,
    preparationTime: 25,
    ingredients: ['Black-eyed Beans', 'Eggs', 'Vegetables', 'Spices'],
    nutritionalInfo: { calories: 220, protein: 12, carbs: 18, fat: 10, fiber: 6 },
    spiceLevel: 'Mild'
  },
  {
    name: 'Puff Puff',
    description: 'Sweet deep-fried dough balls, perfect for snacking',
    price: 800,
    category: 'side-dish',
    images: [{ url: '/images/puff-puff.jpg', alt: 'Puff Puff' }],
    isAvailable: true,
    preparationTime: 20,
    ingredients: ['Flour', 'Sugar', 'Yeast', 'Oil'],
    nutritionalInfo: { calories: 150, protein: 3, carbs: 25, fat: 6, fiber: 1 },
    spiceLevel: 'None'
  }
];

// Seed Menu Items
const seedMenuItems = async () => {
  try {
    console.log('🌱 Starting menu items seeding...');

    // Get the owner to use as createdBy
    const User = (await import('../models/User.js')).default;
    const owner = await User.findOne({ role: 'owner' });
    if (!owner) {
      throw new Error('Owner not found. Please run the main seed script first.');
    }

    // Clear existing menu items
    await MenuItem.deleteMany({});
    console.log('🗑️ Cleared existing menu items');

    // Add createdBy to all menu items
    const menuItemsWithOwner = menuItems.map(item => ({
      ...item,
      createdBy: owner._id
    }));

    // Insert new menu items
    const createdItems = await MenuItem.insertMany(menuItemsWithOwner);
    console.log(`✅ Successfully seeded ${createdItems.length} menu items`);

    // Log categories
    const categories = [...new Set(menuItems.map(item => item.category))];
    console.log(`📂 Categories: ${categories.join(', ')}`);

    return createdItems;
  } catch (error) {
    console.error('❌ Error seeding menu items:', error);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    await connectDB();
    await seedMenuItems();
    
    console.log('🎉 Menu items seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Menu items seeding failed:', error);
    process.exit(1);
  }
};

// Run if called directly
main();



export { seedMenuItems, menuItems };
