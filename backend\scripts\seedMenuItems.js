import mongoose from 'mongoose';
import dotenv from 'dotenv';
import MenuItem from '../models/MenuItem.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zina-chop-house');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Real Menu Items from MenuManagement (with proper asset images)
const menuItems = [
  {
    name: 'Jollof Rice',
    description: 'Traditional West African rice dish cooked in a flavorful tomato-based sauce with spices',
    price: 2500,
    category: 'main-course',
    images: [{ url: '/images/jollof-rice.jpg', alt: 'Jollof Rice' }],
    isAvailable: true,
    preparationTime: 30,
    ingredients: ['Rice', 'Tomatoes', 'Onions', 'Spices'],
    nutritionalInfo: { calories: 350, protein: 8, carbs: 70, fat: 5, fiber: 3 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Fried Rice',
    description: 'Delicious fried rice with mixed vegetables and your choice of protein',
    price: 2200,
    category: 'main-course',
    images: [{ url: '/images/fried-rice.jpg', alt: 'Fried Rice' }],
    isAvailable: true,
    preparationTime: 20,
    ingredients: ['Rice', 'Vegetables', 'Eggs', 'Soy Sauce'],
    nutritionalInfo: { calories: 320, protein: 12, carbs: 55, fat: 8, fiber: 2 },
    spiceLevel: 'Mild',
    isFeatured: false
  },
  {
    name: 'Ndole',
    description: 'Traditional Cameroonian dish with ndole leaves, groundnuts, and assorted meat',
    price: 3500,
    category: 'main-course',
    images: [{ url: '/images/ndole.jpg', alt: 'Ndole' }],
    isAvailable: true,
    preparationTime: 45,
    ingredients: ['Ndole leaves', 'Groundnuts', 'Meat', 'Fish'],
    nutritionalInfo: { calories: 480, protein: 25, carbs: 20, fat: 35, fiber: 8 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Eru',
    description: 'Traditional Cameroonian vegetable soup with eru leaves and assorted meat',
    price: 3200,
    category: 'main-course',
    images: [{ url: '/images/eru.jpg', alt: 'Eru' }],
    isAvailable: true,
    preparationTime: 40,
    ingredients: ['Eru leaves', 'Palm oil', 'Meat', 'Fish'],
    nutritionalInfo: { calories: 420, protein: 22, carbs: 15, fat: 30, fiber: 10 },
    spiceLevel: 'Medium',
    isFeatured: false
  },
  {
    name: 'Grilled Fish',
    description: 'Fresh fish grilled to perfection with local spices and herbs',
    price: 4000,
    category: 'main-course',
    images: [{ url: '/images/grilled-fish.jpg', alt: 'Grilled Fish' }],
    isAvailable: true,
    preparationTime: 25,
    ingredients: ['Fresh fish', 'Spices', 'Herbs'],
    nutritionalInfo: { calories: 280, protein: 35, carbs: 2, fat: 15, fiber: 0 },
    spiceLevel: 'Medium',
    isFeatured: true
  },
  {
    name: 'Pepper Soup',
    description: 'Spicy traditional soup with assorted meat and local spices',
    price: 2800,
    category: 'main-course',
    images: [{ url: '/images/pepper-soup.jpg', alt: 'Pepper Soup' }],
    isAvailable: true,
    preparationTime: 35,
    ingredients: ['Meat', 'Pepper soup spice', 'Vegetables'],
    nutritionalInfo: { calories: 250, protein: 20, carbs: 8, fat: 15, fiber: 2 },
    spiceLevel: 'Hot',
    isFeatured: false
  },
  {
    name: 'Plantain',
    description: 'Sweet fried plantains, perfectly caramelized',
    price: 1000,
    category: 'side-dish',
    images: [{ url: '/images/plantain.jpg', alt: 'Fried Plantain' }],
    isAvailable: true,
    preparationTime: 10,
    ingredients: ['Ripe plantains', 'Oil'],
    nutritionalInfo: { calories: 180, protein: 2, carbs: 40, fat: 6, fiber: 3 },
    spiceLevel: 'None',
    isFeatured: false
  },
  {
    name: 'Beans',
    description: 'Nutritious cooked beans with palm oil and spices',
    price: 1500,
    category: 'side-dish',
    images: [{ url: '/images/beans.jpg', alt: 'Beans' }],
    isAvailable: true,
    preparationTime: 25,
    ingredients: ['Black-eyed beans', 'Palm oil', 'Spices'],
    nutritionalInfo: { calories: 220, protein: 15, carbs: 35, fat: 5, fiber: 12 },
    spiceLevel: 'Mild',
    isFeatured: false
  }
];


// Seed Menu Items
const seedMenuItems = async () => {
  try {
    console.log('🌱 Starting menu items seeding...');

    // Get the owner to use as createdBy
    const User = (await import('../models/User.js')).default;
    const owner = await User.findOne({ role: 'owner' });
    if (!owner) {
      throw new Error('Owner not found. Please run the main seed script first.');
    }

    // Clear existing menu items
    await MenuItem.deleteMany({});
    console.log('🗑️ Cleared existing menu items');

    // Add createdBy to all menu items
    const menuItemsWithOwner = menuItems.map(item => ({
      ...item,
      createdBy: owner._id
    }));

    // Insert new menu items
    const createdItems = await MenuItem.insertMany(menuItemsWithOwner);
    console.log(`✅ Successfully seeded ${createdItems.length} menu items`);

    // Log categories
    const categories = [...new Set(menuItems.map(item => item.category))];
    console.log(`📂 Categories: ${categories.join(', ')}`);

    return createdItems;
  } catch (error) {
    console.error('❌ Error seeding menu items:', error);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    await connectDB();
    await seedMenuItems();
    
    console.log('🎉 Menu items seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Menu items seeding failed:', error);
    process.exit(1);
  }
};

// Run if called directly
main();



export { seedMenuItems, menuItems };
