import api from './api.js';

/**
 * Menu Service - Handles all menu-related API calls
 */
class MenuService {
  /**
   * Get all menu items with optional filters
   */
  async getMenuItems(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.category) params.append('category', filters.category);
      if (filters.search) params.append('search', filters.search);
      if (filters.isAvailable !== undefined) params.append('isAvailable', filters.isAvailable);
      if (filters.minPrice) params.append('minPrice', filters.minPrice);
      if (filters.maxPrice) params.append('maxPrice', filters.maxPrice);
      if (filters.spiceLevel) params.append('spiceLevel', filters.spiceLevel);
      if (filters.dayOfWeek) params.append('dayOfWeek', filters.dayOfWeek);
      
      const queryString = params.toString();
      const url = queryString ? `/menu?${queryString}` : '/menu';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching menu items:', error);
      throw error;
    }
  }

  /**
   * Get a single menu item by ID
   */
  async getMenuItem(id) {
    try {
      const response = await api.get(`/menu/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching menu item:', error);
      throw error;
    }
  }

  /**
   * Get menu categories
   */
  async getCategories() {
    try {
      const response = await api.get('/menu/categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  /**
   * Search menu items
   */
  async searchMenuItems(query, filters = {}) {
    try {
      const params = new URLSearchParams({
        search: query,
        ...filters
      });
      
      const response = await api.get(`/menu/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching menu items:', error);
      throw error;
    }
  }

  /**
   * Get featured/popular menu items
   */
  async getFeaturedItems() {
    try {
      const response = await api.get('/menu/featured');
      return response.data;
    } catch (error) {
      console.error('Error fetching featured items:', error);
      throw error;
    }
  }

  /**
   * Get menu items by category
   */
  async getItemsByCategory(category) {
    try {
      const response = await api.get(`/menu/category/${category}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching items by category:', error);
      throw error;
    }
  }

  /**
   * Get daily specials
   */
  async getDailySpecials(day = null) {
    try {
      const currentDay = day || new Date().toLocaleLowerCase().slice(0, 3);
      const response = await api.get(`/menu/specials/${currentDay}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching daily specials:', error);
      throw error;
    }
  }

  /**
   * Check item availability
   */
  async checkAvailability(itemId) {
    try {
      const response = await api.get(`/menu/${itemId}/availability`);
      return response.data;
    } catch (error) {
      console.error('Error checking availability:', error);
      throw error;
    }
  }

  /**
   * Get nutritional information
   */
  async getNutritionalInfo(itemId) {
    try {
      const response = await api.get(`/menu/${itemId}/nutrition`);
      return response.data;
    } catch (error) {
      console.error('Error fetching nutritional info:', error);
      throw error;
    }
  }

  /**
   * Get menu statistics (for admin)
   */
  async getMenuStats() {
    try {
      const response = await api.get('/menu/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching menu stats:', error);
      throw error;
    }
  }

  /**
   * Create new menu item (admin only)
   */
  async createMenuItem(itemData) {
    try {
      const response = await api.post('/menu', itemData);
      return response.data;
    } catch (error) {
      console.error('Error creating menu item:', error);
      throw error;
    }
  }

  /**
   * Update menu item (admin only)
   */
  async updateMenuItem(id, itemData) {
    try {
      const response = await api.put(`/menu/${id}`, itemData);
      return response.data;
    } catch (error) {
      console.error('Error updating menu item:', error);
      throw error;
    }
  }

  /**
   * Delete menu item (admin only)
   */
  async deleteMenuItem(id) {
    try {
      const response = await api.delete(`/menu/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting menu item:', error);
      throw error;
    }
  }

  /**
   * Update item availability (admin only)
   */
  async updateAvailability(id, isAvailable) {
    try {
      const response = await api.patch(`/menu/${id}/availability`, { isAvailable });
      return response.data;
    } catch (error) {
      console.error('Error updating availability:', error);
      throw error;
    }
  }

  /**
   * Bulk update menu items (admin only)
   */
  async bulkUpdateItems(updates) {
    try {
      const response = await api.patch('/menu/bulk-update', { updates });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating items:', error);
      throw error;
    }
  }

  /**
   * Upload menu item image
   */
  async uploadImage(itemId, imageFile) {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);
      
      const response = await api.post(`/menu/${itemId}/image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  /**
   * Get menu recommendations based on user preferences
   */
  async getRecommendations(userId = null) {
    try {
      const url = userId ? `/menu/recommendations?userId=${userId}` : '/menu/recommendations';
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
const menuService = new MenuService();
export default menuService;
