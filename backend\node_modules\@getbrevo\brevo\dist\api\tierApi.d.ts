import http from 'http';
import { CreateTierGroupRequest } from '../model/createTierGroupRequest';
import { LoyaltyTierPage } from '../model/loyaltyTierPage';
import { Tier } from '../model/tier';
import { TierForContact } from '../model/tierForContact';
import { TierGroup } from '../model/tierGroup';
import { TierGroupPage } from '../model/tierGroupPage';
import { TierRequest } from '../model/tierRequest';
import { TierRequestPutPayload } from '../model/tierRequestPutPayload';
import { UpdateTierGroupRequest } from '../model/updateTierGroupRequest';
import { Authentication, Interceptor } from '../model/models';
import { ApiKeyAuth } from '../model/models';
export declare enum TierApiApiKeys {
    apiKey = 0,
    partnerKey = 1
}
export declare class TierApi {
    protected _basePath: string;
    protected _defaultHeaders: any;
    protected _useQuerystring: boolean;
    protected authentications: {
        default: Authentication;
        apiKey: ApiKeyAuth;
        partnerKey: Api<PERSON>eyA<PERSON>;
    };
    protected interceptors: Interceptor[];
    constructor(basePath?: string);
    set useQuerystring(value: boolean);
    set basePath(basePath: string);
    set defaultHeaders(defaultHeaders: any);
    get defaultHeaders(): any;
    get basePath(): string;
    setDefaultAuthentication(auth: Authentication): void;
    setApiKey(key: TierApiApiKeys, value: string): void;
    addInterceptor(interceptor: Interceptor): void;
    addSubscriptionToTier(pid: string, cid: string, tid: string, options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: TierForContact;
    }>;
    createTierForTierGroup(pid: string, gid: string, payload: TierRequest, options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: Tier;
    }>;
    createTierGroup(pid: string, payload: CreateTierGroupRequest, options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: TierGroup;
    }>;
    deleteTier(pid: string, tid: string, options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: string;
    }>;
    deleteTierGroup(pid: string, gid: string, options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: string;
    }>;
    getListOfTierGroups(pid: string, version?: 'active' | 'draft', options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: TierGroupPage;
    }>;
    getLoyaltyProgramTier(pid: string, version?: 'active' | 'draft', options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: LoyaltyTierPage;
    }>;
    getTierGroup(pid: string, gid: string, version?: 'active' | 'draft', options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: TierGroup;
    }>;
    updateTier(pid: string, tid: string, payload: TierRequestPutPayload, options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: Tier;
    }>;
    updateTierGroup(pid: string, gid: string, payload: UpdateTierGroupRequest, options?: {
        headers: {
            [name: string]: string;
        };
    }): Promise<{
        response: http.IncomingMessage;
        body: TierGroup;
    }>;
}
