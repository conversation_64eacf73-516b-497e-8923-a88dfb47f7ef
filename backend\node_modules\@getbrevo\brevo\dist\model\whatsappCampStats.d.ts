export declare class WhatsappCampStats {
    'sent': number;
    'delivered': number;
    'read': number;
    'unsubscribe': number;
    'notSent': number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
