export declare class Subscription {
    'contactId'?: number;
    'createdAt'?: string;
    'loyaltyProgramId'?: string;
    'loyaltySubscriptionId'?: string;
    'organizationId'?: number;
    'updatedAt'?: string;
    'versionId'?: number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
