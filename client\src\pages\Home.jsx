import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { demoLogin, logout } from '../store/slices/authSlice';
import {
  ClockIcon,
  StarIcon,
  TruckIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';
import menuService from '../services/menuService';
import orderService from '../services/orderService';
import { jollofRice } from '../assets';

const Home = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const [featuredItems, setFeaturedItems] = useState([]);
  const [popularItems, setPopularItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load dynamic data
  useEffect(() => {
    const loadHomeData = async () => {
      try {
        setIsLoading(true);

        // Load featured items
        const featuredResponse = await menuService.getFeaturedItems();
        setFeaturedItems(featuredResponse.data || []);

        // Load popular items
        const popularResponse = await orderService.getPopularItems({ limit: 4 });
        setPopularItems(popularResponse.data || []);

      } catch (error) {
        console.error('Error loading home data:', error);
        // Set fallback data
        setFeaturedItems([]);
        setPopularItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadHomeData();
  }, []);

  const handleDemoLogin = (role) => {
    dispatch(demoLogin({ role }));
  };

  const handleLogout = () => {
    dispatch(logout());
  };

  const features = [
    {
      icon: <ClockIcon className="h-8 w-8 text-orange-500" />,
      title: 'Fast Delivery',
      description: 'Fresh meals delivered within 30-45 minutes'
    },
    {
      icon: <StarIcon className="h-8 w-8 text-orange-500" />,
      title: 'Quality Food',
      description: 'Authentic Cameroonian cuisine made with love'
    },
    {
      icon: <TruckIcon className="h-8 w-8 text-orange-500" />,
      title: 'Free Delivery',
      description: 'Free delivery on orders above 5000 XAF'
    },
    {
      icon: <PhoneIcon className="h-8 w-8 text-orange-500" />,
      title: '24/7 Support',
      description: 'Customer support available round the clock'
    }
  ];

  // Use dynamic data or fallback to static data
  const weeklySpecials = featuredItems.length > 0 ? featuredItems.slice(0, 4).map(item => ({
    day: item.availability?.daysOfWeek?.join(' & ') || 'Available',
    meal: item.name,
    image: item.images?.[0]?.url || '/images/placeholder.jpg',
    price: `${item.price.toLocaleString()} XAF`
  })) : [
    {
      day: 'Monday & Wednesday',
      meal: 'Fried Rice with Chicken',
      image: '/images/fried-rice.jpg',
      price: '2500 XAF'
    },
    {
      day: 'Tuesday & Thursday',
      meal: 'Water Fufu and Eru',
      image: '/images/water-fufu.jpg',
      price: '3000 XAF'
    },
    {
      day: 'Njama Njama Friday',
      meal: 'Fufu and Njama Njama',
      image: '/images/ndole.jpg',
      price: '3500 XAF'
    },
    {
      day: 'Saturday',
      meal: 'Congo Meat with Plantains',
      image: '/images/suya.jpg',
      price: '4000 XAF'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="absolute inset-0 bg-black opacity-40"></div>
        <div
          className="relative min-h-screen flex items-center justify-center bg-cover bg-center"
          style={{
            backgroundImage: `url(${jollofRice})`,
            backgroundBlendMode: 'overlay'
          }}
        >
          <div className="text-center px-4 max-w-4xl mx-auto">
            <motion.h1 
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-5xl md:text-7xl font-bold mb-6"
            >
              {t('welcome')}
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl md:text-2xl mb-8 text-gray-100"
            >
              {t('menuDescription')}
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="space-x-4"
            >
              <Link
                to="/menu"
                className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 rounded-full text-lg font-semibold transition-colors inline-block"
              >
                {t('orderNow')}
              </Link>
              <Link
                to="/special-orders"
                className="border-2 border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4 rounded-full text-lg font-semibold transition-colors inline-block"
              >
                {t('specialOrders')}
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Zina Chop House?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Experience the best of Cameroonian cuisine with our commitment to quality, speed, and customer satisfaction.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow"
              >
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Weekly Menu Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('weeklyMenu')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover our carefully crafted weekly menu featuring authentic Cameroonian dishes
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {weeklySpecials.map((special, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="h-48 bg-gray-200 relative">
                  <img
                    src={special.image}
                    alt={special.meal}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = grilledFish;
                    }}
                  />
                  <div className="absolute top-4 left-4 bg-orange-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {special.price}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {special.meal}
                  </h3>
                  <p className="text-orange-600 font-medium mb-4">
                    {special.day}
                  </p>
                  <Link
                    to="/menu"
                    className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors text-center block"
                  >
                    {t('orderNow')}
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-orange-600 text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Order?
          </h2>
          <p className="text-xl mb-8 text-orange-100">
            Experience the authentic taste of Cameroon delivered right to your doorstep
          </p>
          <div className="space-x-4">
            <Link
              to="/menu"
              className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 rounded-full text-lg font-semibold transition-colors inline-block"
            >
              Browse Menu
            </Link>
            <Link
              to="/special-orders"
              className="border-2 border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4 rounded-full text-lg font-semibold transition-colors inline-block"
            >
              Special Orders
            </Link>
          </div>
        </div>
      </section>

      {/* Demo Login Section */}
      <section className="py-16 bg-gray-100">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            🚀 Demo Access - Test All Features
          </h2>
          <p className="text-gray-600 mb-8">
            Login as different user types to explore all features of the application
          </p>

          {!isAuthenticated ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-xl font-semibold text-gray-900 mb-4">👤 Customer</h3>
                <p className="text-gray-600 mb-4">Browse menu, place orders, track deliveries</p>
                <button
                  onClick={() => handleDemoLogin('customer')}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Login as Customer
                </button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-xl font-semibold text-gray-900 mb-4">👨‍💼 Owner</h3>
                <p className="text-gray-600 mb-4">Manage menu, orders, users, analytics</p>
                <button
                  onClick={() => handleDemoLogin('owner')}
                  className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors"
                >
                  Login as Owner
                </button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-xl font-semibold text-gray-900 mb-4">🚚 Delivery</h3>
                <p className="text-gray-600 mb-4">View assigned orders, update delivery status</p>
                <button
                  onClick={() => handleDemoLogin('delivery')}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                >
                  Login as Delivery Partner
                </button>
              </motion.div>
            </div>
          ) : (
            <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Welcome, {user?.name}!
              </h3>
              <p className="text-gray-600 mb-4">
                You're logged in as: <span className="font-semibold capitalize">{user?.role}</span>
              </p>
              <div className="space-y-3">
                {user?.role === 'owner' && (
                  <Link
                    to="/owner"
                    className="block w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    Go to Owner Dashboard
                  </Link>
                )}
                {user?.role === 'delivery' && (
                  <Link
                    to="/delivery"
                    className="block w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Go to Delivery Dashboard
                  </Link>
                )}
                {user?.role === 'customer' && (
                  <Link
                    to="/orders"
                    className="block w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View My Orders
                  </Link>
                )}
                <button
                  onClick={handleLogout}
                  className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Home;
