export declare class SubscriptionAttributedReward {
    'code'?: string;
    'contactId'?: number;
    'createdAt'?: string;
    'expirationDate'?: string;
    'id'?: string;
    'loyaltyProgramId'?: string;
    'meta'?: {
        [key: string]: object;
    };
    'rewardId'?: string;
    'updatedAt'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
