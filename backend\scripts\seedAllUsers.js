import mongoose from 'mongoose';
import User from '../models/User.js';
import emailService from '../services/emailService.js';
import dotenv from 'dotenv';

dotenv.config();

const seedAllUsers = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    
    // Clear existing users
    await User.deleteMany({});
    console.log('🗑️ Cleared existing users');
    
    // Create users
    const users = [
      // Owner
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: '123456',
        phone: '+237123456789',
        role: 'owner',
        emailVerified: true,
        phoneVerified: true,
        isActive: true
      },
      // Delivery Partner
      {
        name: 'Osca Delivery',
        email: '<EMAIL>',
        password: '123456',
        phone: '+237555123456',
        role: 'delivery',
        emailVerified: true,
        phoneVerified: true,
        isActive: true,
        deliveryInfo: {
          vehicleType: 'motorcycle',
          licenseNumber: 'DL123456',
          isAvailable: true
        }
      },
      // Customer
      {
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: '123456',
        phone: '+237666789012',
        role: 'customer',
        emailVerified: true,
        phoneVerified: true,
        isActive: true,
        address: {
          street: '123 Main Street',
          city: 'Douala',
          state: 'Littoral',
          country: 'Cameroon'
        }
      }
    ];

    console.log('👥 Creating users...');
    const createdUsers = [];
    
    for (const userData of users) {
      const user = new User(userData);
      await user.save();
      createdUsers.push(user);
      console.log(`✅ Created ${userData.role}: ${userData.email}`);
    }
    
    // Send welcome emails
    try {
      console.log('📧 Sending welcome emails...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for email service
      
      // Send email to owner
      const owner = createdUsers.find(user => user.role === 'owner');
      if (owner) {
        await emailService.sendOwnerWelcomeEmail({
          ownerEmail: owner.email,
          ownerName: owner.name,
          password: '123456'
        });
        console.log('📧 Welcome email sent to owner');
      }
      
      // Send email to customer
      const customer = createdUsers.find(user => user.role === 'customer');
      if (customer) {
        await emailService.sendWelcomeEmail({
          customerEmail: customer.email,
          customerName: customer.name
        });
        console.log('📧 Welcome email sent to customer');
      }
      
    } catch (emailError) {
      console.warn('⚠️ Failed to send some welcome emails:', emailError.message);
    }
    
    console.log('\n🎉 All users created successfully!');
    console.log('\n🔑 Login Credentials:');
    console.log('👑 Owner: <EMAIL> / 123456');
    console.log('🚚 Delivery: <EMAIL> / password123');
    console.log('👤 Customer: <EMAIL> / customer123');
    
  } catch (error) {
    console.error('❌ Error seeding users:', error);
  } finally {
    mongoose.disconnect();
  }
};

seedAllUsers();
