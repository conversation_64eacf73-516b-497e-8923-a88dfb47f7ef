export declare class MainProductPayload {
    'category'?: Array<string>;
    'price'?: number;
    'productId'?: string;
    'quantity'?: number;
    'variantId'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
