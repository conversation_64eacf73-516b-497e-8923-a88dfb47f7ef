import { MainRuleConditionResponse } from './mainRuleConditionResponse';
import { MainRuleEventResponse } from './mainRuleEventResponse';
import { MainRuleResultResponse } from './mainRuleResultResponse';
export declare class MainRule {
    'condition'?: MainRuleConditionResponse;
    'createdAt'?: string;
    'description'?: string;
    'event'?: MainRuleEventResponse;
    'isInternal'?: boolean;
    'loyaltyProgramId'?: string;
    'loyaltyVersionId'?: number;
    'meta'?: {
        [key: string]: any;
    };
    'name'?: string;
    'results'?: Array<MainRuleResultResponse>;
    'ruleId'?: string;
    'ruleType'?: string;
    'updatedAt'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
