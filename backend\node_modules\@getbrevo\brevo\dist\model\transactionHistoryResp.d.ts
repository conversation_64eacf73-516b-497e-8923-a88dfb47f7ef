import { TransactionHistory } from './transactionHistory';
export declare class TransactionHistoryResp {
    'balanceDefinitionId'?: string;
    'contactId'?: number;
    'count'?: number;
    'loyaltyProgramId'?: string;
    'transactionHistory'?: Array<TransactionHistory>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
