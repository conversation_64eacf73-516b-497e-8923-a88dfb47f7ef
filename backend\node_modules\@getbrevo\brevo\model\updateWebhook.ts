/**
 * Brevo API
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity | 
 *
 * The version of the OpenAPI document: 3.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from './models';
import { GetWebhookAuth } from './getWebhookAuth';
import { GetWebhookHeadersInner } from './getWebhookHeadersInner';

export class UpdateWebhook {
    /**
    * URL of the webhook
    */
    'url'?: string;
    /**
    * Description of the webhook
    */
    'description'?: string;
    /**
    * - Events triggering the webhook. Possible values for **Transactional** type webhook: #### `sent` OR `request`, `delivered`, `hardBounce`, `softBounce`, `blocked`, `spam`, `invalid`, `deferred`, `click`, `opened`, `uniqueOpened` and `unsubscribed` - Possible values for **Marketing** type webhook: #### `spam`, `opened`, `click`, `hardBounce`, `softBounce`, `unsubscribed`, `listAddition` & `delivered` - Possible values for **Inbound** type webhook: #### `inboundEmailProcessed` 
    */
    'events'?: Array<UpdateWebhook.EventsEnum>;
    /**
    * Inbound domain of webhook, used in case of event type `inbound`
    */
    'domain'?: string;
    /**
    * To send batched webhooks
    */
    'batched'?: boolean;
    'auth'?: GetWebhookAuth;
    /**
    * Custom headers to be send with webhooks
    */
    'headers'?: Array<GetWebhookHeadersInner>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "url",
            "baseName": "url",
            "type": "string"
        },
        {
            "name": "description",
            "baseName": "description",
            "type": "string"
        },
        {
            "name": "events",
            "baseName": "events",
            "type": "Array<UpdateWebhook.EventsEnum>"
        },
        {
            "name": "domain",
            "baseName": "domain",
            "type": "string"
        },
        {
            "name": "batched",
            "baseName": "batched",
            "type": "boolean"
        },
        {
            "name": "auth",
            "baseName": "auth",
            "type": "GetWebhookAuth"
        },
        {
            "name": "headers",
            "baseName": "headers",
            "type": "Array<GetWebhookHeadersInner>"
        }    ];

    static getAttributeTypeMap() {
        return UpdateWebhook.attributeTypeMap;
    }
}

export namespace UpdateWebhook {
    export enum EventsEnum {
        Sent = <any> 'sent',
        HardBounce = <any> 'hardBounce',
        SoftBounce = <any> 'softBounce',
        Blocked = <any> 'blocked',
        Spam = <any> 'spam',
        Delivered = <any> 'delivered',
        Request = <any> 'request',
        Click = <any> 'click',
        Invalid = <any> 'invalid',
        Deferred = <any> 'deferred',
        Opened = <any> 'opened',
        UniqueOpened = <any> 'uniqueOpened',
        Unsubscribed = <any> 'unsubscribed',
        ListAddition = <any> 'listAddition',
        ContactUpdated = <any> 'contactUpdated',
        ContactDeleted = <any> 'contactDeleted',
        InboundEmailProcessed = <any> 'inboundEmailProcessed'
    }
}
