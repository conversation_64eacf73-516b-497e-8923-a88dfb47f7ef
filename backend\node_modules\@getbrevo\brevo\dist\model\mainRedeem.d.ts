export declare class MainRedeem {
    'cancelledAt'?: Date;
    'completedAt'?: Date;
    'contactId'?: number;
    'createdAt'?: Date;
    'debitTransactionId'?: string;
    'expiresAt'?: Date;
    'id'?: string;
    'loyaltyProgramId'?: string;
    'meta'?: {
        [key: string]: object;
    };
    'rejectReason'?: string;
    'rejectedAt'?: Date;
    'rewardAttributionId'?: string;
    'status'?: string;
    'updatedAt'?: Date;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
