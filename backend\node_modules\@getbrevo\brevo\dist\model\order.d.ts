import { OrderBilling } from './orderBilling';
import { OrderIdentifiers } from './orderIdentifiers';
import { OrderProductsInner } from './orderProductsInner';
export declare class Order {
    'id': string;
    'createdAt': string;
    'updatedAt': string;
    'status': string;
    'amount': number;
    'storeId'?: string;
    'identifiers'?: OrderIdentifiers;
    'products': Array<OrderProductsInner>;
    'billing'?: OrderBilling;
    'coupons'?: Array<string>;
    'metaInfo'?: {
        [key: string]: object;
    };
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
