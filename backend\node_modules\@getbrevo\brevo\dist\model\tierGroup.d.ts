export declare class TierGroup {
    'id'?: string;
    'name'?: string;
    'tierOrder'?: Array<string>;
    'loyaltyProgramId'?: string;
    'upgradeStrategy'?: TierGroup.UpgradeStrategyEnum;
    'downgradeStrategy'?: TierGroup.DowngradeStrategyEnum;
    'createdAt'?: Date;
    'updatedAt'?: Date;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace TierGroup {
    enum UpgradeStrategyEnum {
        RealTime,
        MembershipAnniversary,
        TierAnniversary
    }
    enum DowngradeStrategyEnum {
        RealTime,
        MembershipAnniversary,
        TierAnniversary
    }
}
