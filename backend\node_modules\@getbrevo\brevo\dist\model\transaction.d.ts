export declare class Transaction {
    'amount'?: number;
    'balanceDefinitionId'?: string;
    'cancelledAt'?: string;
    'completedAt'?: string;
    'contactId'?: number;
    'createdAt'?: string;
    'eventTime'?: string;
    'expirationDate'?: string;
    'id'?: string;
    'loyaltyProgramId'?: string;
    'meta'?: {
        [key: string]: any;
    };
    'rejectReason'?: string;
    'rejectedAt'?: string;
    'status'?: string;
    'updatedAt'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
