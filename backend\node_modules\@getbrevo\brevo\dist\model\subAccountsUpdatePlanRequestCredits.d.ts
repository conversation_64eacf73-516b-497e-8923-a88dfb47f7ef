export declare class SubAccountsUpdatePlanRequestCredits {
    'email'?: number;
    'sms'?: number;
    'wpSubscribers'?: number;
    'externalFeeds'?: number;
    'whatsapp'?: number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
