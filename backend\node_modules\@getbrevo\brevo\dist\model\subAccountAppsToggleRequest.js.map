{"version": 3, "file": "subAccountAppsToggleRequest.js", "sourceRoot": "", "sources": ["../../model/subAccountAppsToggleRequest.ts"], "names": [], "mappings": ";;;AAiBA,MAAa,2BAA2B;IA2HpC,MAAM,CAAC,mBAAmB;QACtB,OAAO,2BAA2B,CAAC,gBAAgB,CAAC;IACxD,CAAC;;AA7HL,kEA8HC;AAxEU,yCAAa,GAAuB,SAAS,CAAC;AAE9C,4CAAgB,GAA0D;IAC7E;QACI,MAAM,EAAE,OAAO;QACf,UAAU,EAAE,OAAO;QACnB,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,UAAU;QAClB,UAAU,EAAE,UAAU;QACtB,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,YAAY;QACpB,UAAU,EAAE,YAAY;QACxB,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,gBAAgB;QACxB,UAAU,EAAE,iBAAiB;QAC7B,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,cAAc;QACtB,UAAU,EAAE,eAAe;QAC3B,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,cAAc;QACtB,UAAU,EAAE,eAAe;QAC3B,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,qBAAqB;QAC7B,UAAU,EAAE,sBAAsB;QAClC,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,kBAAkB;QAC1B,UAAU,EAAE,mBAAmB;QAC/B,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,aAAa;QACrB,UAAU,EAAE,cAAc;QAC1B,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,UAAU;QACtB,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,UAAU;QAClB,UAAU,EAAE,UAAU;QACtB,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,eAAe;QACvB,UAAU,EAAE,eAAe;QAC3B,MAAM,EAAE,SAAS;KACpB;IACD;QACI,MAAM,EAAE,KAAK;QACb,UAAU,EAAE,KAAK;QACjB,MAAM,EAAE,SAAS;KACpB;CAAK,CAAC"}