export declare class UpdateBalanceLimitPayload {
    'constraintType': UpdateBalanceLimitPayload.ConstraintTypeEnum;
    'durationUnit': UpdateBalanceLimitPayload.DurationUnitEnum;
    'durationValue': number;
    'slidingSchedule'?: boolean;
    'transactionType': UpdateBalanceLimitPayload.TransactionTypeEnum;
    'value': number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace UpdateBalanceLimitPayload {
    enum ConstraintTypeEnum {
        Transaction,
        Amount
    }
    enum DurationUnitEnum {
        Day,
        Week,
        Month,
        Year
    }
    enum TransactionTypeEnum {
        Credit,
        Debit
    }
}
