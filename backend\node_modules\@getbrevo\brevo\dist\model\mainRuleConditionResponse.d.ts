import { MainValueResponse } from './mainValueResponse';
export declare class MainRuleConditionResponse {
    'and'?: Array<MainRuleConditionResponse>;
    'lhs'?: MainValueResponse;
    'op'?: string;
    'or'?: Array<MainRuleConditionResponse>;
    'rhs'?: MainValueResponse;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
