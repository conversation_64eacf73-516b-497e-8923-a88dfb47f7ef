import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon 
} from '@heroicons/react/24/outline';
import { addToCart } from '../store/slices/cartSlice';
import { setMenuItems, setFilters, setLoading } from '../store/slices/menuSlice';
import toast from 'react-hot-toast';
import menuService from '../services/menuService';
import {
  jollofRice,
  friedRice,
  waterFufu,
  ndole,
  grilledFish,
  friedPlantains,
  pepperSoup,
  akara
} from '../assets';

const Menu = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { items, filters, isLoading } = useSelector((state) => state.menu);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedDay, setSelectedDay] = useState('');

  // Map item names to imported images
  const getImageForItem = (itemName) => {
    const imageMap = {
      'Jollof Rice': jollofRice,
      'Fried Rice': friedRice,
      'Ndole': ndole,
      'Eru': waterFufu, // Using waterFufu image for Eru
      'Grilled Fish': grilledFish,
      'Pepper Soup': pepperSoup,
      'Plantain': friedPlantains,
      'Beans': akara // Using akara image for beans
    };
    return imageMap[itemName] || jollofRice; // Default fallback
  };

  // Load menu items from API
  useEffect(() => {
    loadMenuItems();
  }, [selectedCategory, selectedDay]);

  const loadMenuItems = async () => {
    try {
      dispatch(setLoading(true));

      const filters = {};
      if (selectedCategory) filters.category = selectedCategory;
      if (selectedDay) filters.dayOfWeek = selectedDay;
      if (searchTerm) filters.search = searchTerm;

      const response = await menuService.getMenuItems(filters);
      dispatch(setMenuItems(response.data || []));
    } catch (error) {
      console.error('Error loading menu items:', error);
      toast.error(t('errorLoadingMenu') || 'Error loading menu items');
      // Fallback to empty array if API fails
      dispatch(setMenuItems([]));
    } finally {
      dispatch(setLoading(false));
    }
  };

  // Search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== filters.search) {
        dispatch(setFilters({ ...filters, search: searchTerm }));
        loadMenuItems();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Categories for filtering

  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'main-course', label: 'Main Course' },
    { value: 'side-dish', label: 'Side Dishes' },
    { value: 'special', label: 'Specials' },
    { value: 'beverage', label: 'Beverages' },
    { value: 'dessert', label: 'Desserts' }
  ];

  const days = [
    { value: '', label: 'All Days' },
    { value: 'monday', label: t('monday') },
    { value: 'tuesday', label: t('tuesday') },
    { value: 'wednesday', label: t('wednesday') },
    { value: 'thursday', label: t('thursday') },
    { value: 'friday', label: t('friday') },
    { value: 'saturday', label: t('saturday') },
    { value: 'sunday', label: t('sunday') }
  ];

  // Since filtering is now done on the server side, we just use the items from the store
  // Additional client-side filtering can be added here if needed
  const filteredItems = items || [];

  const handleAddToCart = (menuItem) => {
    dispatch(addToCart({ menuItem, quantity: 1 }));
    toast.success(`${menuItem.name} added to cart!`);
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-yellow-400">★</span>);
    }
    if (hasHalfStar) {
      stars.push(<span key="half" className="text-yellow-400">☆</span>);
    }
    for (let i = stars.length; i < 5; i++) {
      stars.push(<span key={i} className="text-gray-300">☆</span>);
    }
    return stars;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('menuTitle')}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('menuDescription')}
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              {categories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>

            {/* Day Filter */}
            <select
              value={selectedDay}
              onChange={(e) => setSelectedDay(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              {days.map(day => (
                <option key={day.value} value={day.value}>
                  {day.label}
                </option>
              ))}
            </select>

            {/* Clear Filters */}
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('');
                setSelectedDay('');
              }}
              className="px-4 py-2 text-orange-600 border border-orange-600 rounded-lg hover:bg-orange-50 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>

        {/* Menu Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
            >
              {/* Image */}
              <div className="h-48 bg-gray-200 relative">
                <img
                  src={getImageForItem(item.name)}
                  alt={item.images[0]?.alt || item.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = jollofRice;
                  }}
                />
                {item.isSpecial && (
                  <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Special
                  </div>
                )}
                <div className="absolute top-4 right-4 bg-orange-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {item.price} XAF
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {item.name}
                </h3>
                <p className="text-gray-600 mb-4 text-sm">
                  {item.description}
                </p>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center">
                    {renderStars(item.rating.average)}
                  </div>
                  <span className="ml-2 text-sm text-gray-500">
                    ({item.rating.count} reviews)
                  </span>
                </div>

                {/* Availability */}
                <div className="mb-4">
                  <span className="text-sm text-gray-500">Available: </span>
                  <span className="text-sm text-orange-600">
                    {item.availability.daysOfWeek.length === 0 
                      ? 'All days' 
                      : item.availability.daysOfWeek.map(day => 
                          day.charAt(0).toUpperCase() + day.slice(1)
                        ).join(', ')
                    }
                  </span>
                </div>

                {/* Add to Cart Button */}
                <button
                  onClick={() => handleAddToCart(item)}
                  className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  {t('addToCart')}
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* No Results */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              No menu items found matching your criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Menu;
