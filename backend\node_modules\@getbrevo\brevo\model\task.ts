/**
 * Brevo API
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity | 
 *
 * The version of the OpenAPI document: 3.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from './models';

/**
* Task Details
*/
export class Task {
    /**
    * Unique task id
    */
    'id'?: string;
    /**
    * Id for type of task e.g Call / Email / Meeting etc.
    */
    'taskTypeId': string;
    /**
    * Name of task
    */
    'name': string;
    /**
    * Contact ids for contacts linked to this task
    */
    'contactsIds'?: Array<number>;
    /**
    * Deal ids for deals a task is linked to
    */
    'dealsIds'?: Array<string>;
    /**
    * Companies ids for companies a task is linked to
    */
    'companiesIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "taskTypeId",
            "baseName": "taskTypeId",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "contactsIds",
            "baseName": "contactsIds",
            "type": "Array<number>"
        },
        {
            "name": "dealsIds",
            "baseName": "dealsIds",
            "type": "Array<string>"
        },
        {
            "name": "companiesIds",
            "baseName": "companiesIds",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Task.attributeTypeMap;
    }
}

