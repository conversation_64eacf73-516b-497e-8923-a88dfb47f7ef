export declare class MainValidateRewardPayload {
    'attributedRewardId'?: string;
    'code'?: string;
    'contactId'?: number;
    'loyaltySubscriptionId'?: string;
    'pointOfSellId'?: string;
    'rewardId'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
