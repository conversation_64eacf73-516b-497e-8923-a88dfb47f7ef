export declare class MainRewardAttribution {
    'value'?: number;
    'code'?: string;
    'consumedAt'?: string;
    'contactId'?: number;
    'createdAt'?: Date;
    'expirationDate'?: Date;
    'id'?: string;
    'loyaltyProgramId'?: string;
    'meta'?: {
        [key: string]: object;
    };
    'rewardId'?: string;
    'updatedAt'?: Date;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
