export declare class TierForContact {
    'id'?: string;
    'loyaltyProgramId'?: string;
    'groupId'?: string;
    'contactId'?: number;
    'meta'?: {
        [key: string]: object;
    };
    'createdAt'?: Date;
    'updatedAt'?: Date;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
