export declare class MainRewardPageObj {
    'createdAt'?: Date;
    'endDate'?: Date;
    'id'?: string;
    'loyaltyProgramId'?: string;
    'name'?: string;
    'publicImage'?: string;
    'startDate'?: Date;
    'state'?: string;
    'updatedAt'?: Date;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
