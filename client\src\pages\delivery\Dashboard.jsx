import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import toast from 'react-hot-toast';
import {
  TruckIcon,
  ClockIcon,
  CheckCircleIcon,
  MapPinIcon,
  PhoneIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import deliveryService from '../../services/deliveryService';

const Dashboard = () => {
  const { user } = useSelector((state) => state.auth);
  const [stats, setStats] = useState({
    totalDeliveries: 0,
    pendingDeliveries: 0,
    completedToday: 0,
    earnings: 0
  });

  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAvailable, setIsAvailable] = useState(true);

  // Fetch real data from backend
  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch stats and orders in parallel
      const [statsResponse, ordersResponse] = await Promise.all([
        deliveryService.getDeliveryStats(),
        deliveryService.getAssignedOrders()
      ]);

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      if (ordersResponse.success) {
        setOrders(ordersResponse.data);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');

      // Fallback to mock data
      setStats({
        totalDeliveries: 156,
        pendingDeliveries: 3,
        completedToday: 8,
        earnings: 12500
      });

      setOrders([
        {
          id: '1',
          orderNumber: 'ZCH1640995200000',
          customerName: 'John Doe',
          customerPhone: '+237123456789',
          address: '123 Main St, Douala',
          totalAmount: 7500,
          status: 'ready-for-pickup',
          estimatedTime: '15 mins',
          items: [
            { name: 'Fried Rice with Chicken', quantity: 2 },
            { name: 'Fried Plantains', quantity: 1 }
          ]
        },
        {
          id: '2',
          orderNumber: 'ZCH1640995300000',
          customerName: 'Jane Smith',
          customerPhone: '+237987654321',
          address: '456 Oak Ave, Douala',
          totalAmount: 6000,
          status: 'out-for-delivery',
          estimatedTime: '25 mins',
          items: [
            { name: 'Water Fufu and Eru', quantity: 2 }
          ]
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleOrderAction = async (orderId, action) => {
    try {
      let response;

      switch (action) {
        case 'accept':
          response = await deliveryService.acceptOrder(orderId);
          break;
        case 'pickup':
          response = await deliveryService.markPickedUp(orderId);
          break;
        case 'deliver':
          response = await deliveryService.markDelivered(orderId);
          break;
        default:
          return;
      }

      if (response.success) {
        toast.success(`Order ${action}ed successfully!`);
        fetchDashboardData(); // Refresh data
      }
    } catch (error) {
      console.error(`Error ${action}ing order:`, error);
      toast.error(`Failed to ${action} order`);
    }
  };

  const toggleAvailability = async () => {
    try {
      const newAvailability = !isAvailable;
      const response = await deliveryService.updateAvailability(newAvailability);

      if (response.success) {
        setIsAvailable(newAvailability);
        toast.success(`You are now ${newAvailability ? 'available' : 'unavailable'} for deliveries`);
      }
    } catch (error) {
      console.error('Error updating availability:', error);
      toast.error('Failed to update availability');
    }
  };
    ]);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'ready-for-pickup':
        return 'bg-yellow-100 text-yellow-800';
      case 'out-for-delivery':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStatusUpdate = (orderId, newStatus) => {
    setOrders(orders.map(order =>
      order.id === orderId ? { ...order, status: newStatus } : order
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Delivery Dashboard
          </h1>
          <div className="text-sm text-gray-500">
            Welcome back, Delivery Partner!
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center">
              <TruckIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Deliveries</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalDeliveries}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingDeliveries}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Completed Today</p>
                <p className="text-2xl font-bold text-gray-900">{stats.completedToday}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-lg shadow-sm p-6"
          >
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Today's Earnings</p>
                <p className="text-2xl font-bold text-gray-900">{stats.earnings} XAF</p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Active Orders */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Active Deliveries</h2>
          </div>

          <div className="divide-y divide-gray-200">
            {orders.map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-6"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        Order #{order.orderNumber}
                      </h3>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-500">Customer</p>
                        <p className="font-medium">{order.customerName}</p>
                        <div className="flex items-center mt-1">
                          <PhoneIcon className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-600">{order.customerPhone}</span>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-gray-500">Delivery Address</p>
                        <div className="flex items-start">
                          <MapPinIcon className="h-4 w-4 text-gray-400 mr-1 mt-0.5" />
                          <span className="text-sm text-gray-600">{order.address}</span>
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <p className="text-sm text-gray-500 mb-2">Items</p>
                      <div className="space-y-1">
                        {order.items.map((item, itemIndex) => (
                          <p key={itemIndex} className="text-sm text-gray-600">
                            {item.name} x{item.quantity}
                          </p>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-500">Total Amount</p>
                        <p className="text-lg font-bold text-orange-600">{order.totalAmount} XAF</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">ETA</p>
                        <p className="font-medium">{order.estimatedTime}</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col space-y-2">
                    {order.status === 'ready-for-pickup' && (
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'out-for-delivery')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                      >
                        Start Delivery
                      </button>
                    )}

                    {order.status === 'out-for-delivery' && (
                      <button
                        onClick={() => handleStatusUpdate(order.id, 'delivered')}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                      >
                        Mark Delivered
                      </button>
                    )}

                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                      Call Customer
                    </button>

                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                      View Map
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
