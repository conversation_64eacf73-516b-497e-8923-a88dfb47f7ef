import { MainGenerator } from './mainGenerator';
import { MainLimit } from './mainLimit';
import { MainProduct } from './mainProduct';
import { MainRewardConfigurations } from './mainRewardConfigurations';
import { MainRule } from './mainRule';
export declare class MainReward {
    'attributionPerConsumer'?: number;
    'balanceDefinitionId'?: string;
    'code'?: string;
    'codeCount'?: number;
    'codeGeneratorId'?: string;
    'codePoolId'?: string;
    'config'?: string;
    'createdAt'?: Date;
    'disabledAt'?: Date;
    'endDate'?: Date;
    'expirationDate'?: Date;
    'expirationModifier'?: MainReward.ExpirationModifierEnum;
    'expirationUnit'?: string;
    'expirationValue'?: number;
    'generator'?: MainGenerator;
    'id'?: string;
    'limits'?: Array<MainLimit>;
    'loyaltyProgramId'?: string;
    'meta'?: {
        [key: string]: object;
    };
    'name'?: string;
    'products'?: Array<MainProduct>;
    'publicDescription'?: string;
    'publicImage'?: string;
    'publicName'?: string;
    'redeemPerConsumer'?: number;
    'redeemRules'?: Array<string>;
    'rewardConfigs'?: MainRewardConfigurations;
    'rule'?: MainRule;
    'startDate'?: Date;
    'subtractBalanceDefinitionId'?: string;
    'subtractBalanceStrategy'?: string;
    'subtractBalanceValue'?: number;
    'subtractTotalBalance'?: boolean;
    'totalAttribution'?: number;
    'totalRedeem'?: number;
    'triggerId'?: string;
    'unit'?: string;
    'updatedAt'?: string;
    'value'?: number;
    'valueType'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace MainReward {
    enum ExpirationModifierEnum {
        StartOfPeriod,
        EndOfPeriod,
        NoModification
    }
}
