import { TierRequestAccessConditionsInner } from './tierRequestAccessConditionsInner';
import { TierRequestTierRewardsInner } from './tierRequestTierRewardsInner';
export declare class TierRequest {
    'name': string;
    'imageRef'?: string;
    'accessConditions': Array<TierRequestAccessConditionsInner>;
    'tierRewards'?: Array<TierRequestTierRewardsInner>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
