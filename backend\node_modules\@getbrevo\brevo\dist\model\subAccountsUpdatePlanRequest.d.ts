import { SubAccountsUpdatePlanRequestCredits } from './subAccountsUpdatePlanRequestCredits';
import { SubAccountsUpdatePlanRequestFeatures } from './subAccountsUpdatePlanRequestFeatures';
export declare class SubAccountsUpdatePlanRequest {
    'subAccountIds'?: Array<number>;
    'credits'?: SubAccountsUpdatePlanRequestCredits;
    'features'?: SubAccountsUpdatePlanRequestFeatures;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
