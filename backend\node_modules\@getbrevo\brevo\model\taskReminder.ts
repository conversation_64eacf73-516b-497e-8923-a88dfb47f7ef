/**
 * Brevo API
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity | 
 *
 * The version of the OpenAPI document: 3.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from './models';

/**
* Task reminder date/time for a task
*/
export class TaskReminder {
    /**
    * Value of time unit before reminder is to be sent
    */
    'value': number;
    /**
    * Unit of time before reminder is to be sent
    */
    'unit': TaskReminder.UnitEnum;
    /**
    * Type of task reminder e.g email, push
    */
    'types': Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "value",
            "baseName": "value",
            "type": "number"
        },
        {
            "name": "unit",
            "baseName": "unit",
            "type": "TaskReminder.UnitEnum"
        },
        {
            "name": "types",
            "baseName": "types",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return TaskReminder.attributeTypeMap;
    }
}

export namespace TaskReminder {
    export enum UnitEnum {
        Minutes = <any> 'minutes',
        Hours = <any> 'hours',
        Weeks = <any> 'weeks',
        Days = <any> 'days'
    }
}
