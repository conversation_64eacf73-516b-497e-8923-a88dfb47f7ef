/**
 * Email Templates for <PERSON><PERSON> House
 * Professional, responsive email templates
 */

const baseStyles = `
  <style>
    @media only screen and (max-width: 600px) {
      .container { width: 100% !important; }
      .content { padding: 15px !important; }
      .header { padding: 15px !important; }
      .footer { padding: 10px !important; }
      h1 { font-size: 24px !important; }
      h2 { font-size: 20px !important; }
    }
    .button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #f97316;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: bold;
      margin: 10px 0;
    }
    .button:hover {
      background-color: #ea580c;
    }
    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
    }
    .status-confirmed { background-color: #dbeafe; color: #1e40af; }
    .status-preparing { background-color: #fef3c7; color: #92400e; }
    .status-ready { background-color: #d1fae5; color: #065f46; }
    .status-delivered { background-color: #dcfce7; color: #166534; }
    .status-cancelled { background-color: #fee2e2; color: #dc2626; }
  </style>
`;

const baseTemplate = (content, title = 'Zina Chop House') => `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  ${baseStyles}
</head>
<body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f9fafb;">
  <div class="container" style="max-width: 600px; margin: 0 auto; background-color: white;">
    ${content}
  </div>
</body>
</html>
`;

export const emailTemplates = {
  /**
   * Order Confirmation Template
   */
  orderConfirmation: (data) => {
    const { customerName, orderId, items, totalAmount, deliveryAddress, estimatedDeliveryTime, orderDate } = data;
    
    const itemsList = items.map(item => `
      <tr style="border-bottom: 1px solid #e5e7eb;">
        <td style="padding: 12px 0; font-weight: 500;">${item.name}</td>
        <td style="padding: 12px 0; text-align: center;">${item.quantity}</td>
        <td style="padding: 12px 0; text-align: right; font-weight: 500;">${item.price.toLocaleString()} XAF</td>
      </tr>
    `).join('');

    const content = `
      <!-- Header -->
      <div class="header" style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; padding: 30px; text-align: center;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Order Confirmed! 🎉</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Thank you for choosing Zina Chop House, ${customerName}!</p>
      </div>

      <!-- Content -->
      <div class="content" style="padding: 30px;">
        <!-- Order Summary -->
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
          <h2 style="margin: 0 0 15px 0; color: #1f2937; font-size: 20px;">Order Summary</h2>
          <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span style="color: #6b7280;">Order ID:</span>
            <span style="font-weight: bold; color: #f97316;">#${orderId}</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span style="color: #6b7280;">Order Date:</span>
            <span style="font-weight: 500;">${orderDate || new Date().toLocaleDateString()}</span>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span style="color: #6b7280;">Estimated Delivery:</span>
            <span style="font-weight: 500; color: #059669;">${estimatedDeliveryTime} minutes</span>
          </div>
        </div>

        <!-- Items -->
        <h3 style="color: #1f2937; margin-bottom: 15px;">Your Order</h3>
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 25px;">
          <thead>
            <tr style="background-color: #f9fafb; border-bottom: 2px solid #e5e7eb;">
              <th style="padding: 12px 0; text-align: left; font-weight: 600; color: #374151;">Item</th>
              <th style="padding: 12px 0; text-align: center; font-weight: 600; color: #374151;">Qty</th>
              <th style="padding: 12px 0; text-align: right; font-weight: 600; color: #374151;">Price</th>
            </tr>
          </thead>
          <tbody>
            ${itemsList}
          </tbody>
        </table>

        <!-- Total -->
        <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin-bottom: 25px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 18px; font-weight: 600; color: #92400e;">Total Amount:</span>
            <span style="font-size: 24px; font-weight: bold; color: #92400e;">${totalAmount.toLocaleString()} XAF</span>
          </div>
        </div>

        ${deliveryAddress ? `
        <!-- Delivery Address -->
        <div style="background-color: #eff6ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; margin-bottom: 25px;">
          <h4 style="margin: 0 0 10px 0; color: #1e40af;">Delivery Address</h4>
          <p style="margin: 0; color: #1e40af; font-weight: 500;">${deliveryAddress}</p>
        </div>
        ` : ''}

        <!-- Next Steps -->
        <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; border-left: 4px solid #22c55e; margin-bottom: 25px;">
          <h4 style="margin: 0 0 15px 0; color: #166534;">What's Next?</h4>
          <ul style="margin: 0; padding-left: 20px; color: #166534;">
            <li style="margin-bottom: 8px;">Our chefs are preparing your delicious meal</li>
            <li style="margin-bottom: 8px;">You'll receive updates as your order progresses</li>
            <li style="margin-bottom: 8px;">Our delivery partner will contact you when on the way</li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div style="text-align: center; padding: 20px 0; border-top: 1px solid #e5e7eb;">
          <p style="margin: 0 0 10px 0; color: #6b7280;">Questions about your order?</p>
          <a href="tel:${process.env.OWNER_PHONE || '+237XXXXXXXXX'}" class="button">Contact Us</a>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer" style="background-color: #1f2937; color: white; padding: 20px; text-align: center;">
        <h3 style="margin: 0 0 10px 0; color: #f97316;">Zina Chop House</h3>
        <p style="margin: 0 0 5px 0; font-size: 14px; opacity: 0.8;">Authentic Cameroonian Cuisine</p>
        <p style="margin: 0; font-size: 14px; opacity: 0.8;">📞 ${process.env.OWNER_PHONE || '+237 XXX XXX XXX'}</p>
      </div>
    `;

    return baseTemplate(content, `Order Confirmation - ${orderId}`);
  },

  /**
   * Order Status Update Template
   */
  orderStatusUpdate: (data) => {
    const { customerName, orderId, status, estimatedTime, message } = data;
    
    const statusConfig = {
      'confirmed': { color: '#3b82f6', icon: '✅', title: 'Order Confirmed' },
      'preparing': { color: '#f59e0b', icon: '👨‍🍳', title: 'Being Prepared' },
      'ready': { color: '#10b981', icon: '🍽️', title: 'Ready for Delivery' },
      'out_for_delivery': { color: '#8b5cf6', icon: '🚗', title: 'Out for Delivery' },
      'delivered': { color: '#059669', icon: '🎉', title: 'Delivered' },
      'cancelled': { color: '#dc2626', icon: '❌', title: 'Cancelled' }
    };

    const config = statusConfig[status] || statusConfig['confirmed'];

    const content = `
      <!-- Header -->
      <div class="header" style="background-color: ${config.color}; color: white; padding: 30px; text-align: center;">
        <div style="font-size: 48px; margin-bottom: 10px;">${config.icon}</div>
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">${config.title}</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Hello ${customerName}!</p>
      </div>

      <!-- Content -->
      <div class="content" style="padding: 30px;">
        <!-- Order Info -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h2 style="margin: 0 0 10px 0; color: #1f2937;">Order #${orderId}</h2>
          <span class="status-badge status-${status}" style="display: inline-block; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; text-transform: uppercase; background-color: ${config.color}20; color: ${config.color};">
            ${status.replace('_', ' ')}
          </span>
        </div>

        <!-- Status Message -->
        <div style="background-color: #f9fafb; padding: 25px; border-radius: 8px; text-align: center; margin-bottom: 25px;">
          <p style="margin: 0; font-size: 16px; color: #374151; line-height: 1.6;">
            ${message || `Your order status has been updated to: ${status.replace('_', ' ')}`}
          </p>
          ${estimatedTime ? `
            <div style="margin-top: 20px; padding: 15px; background-color: white; border-radius: 6px; border: 1px solid #e5e7eb;">
              <span style="color: #6b7280;">Estimated time:</span>
              <span style="font-weight: bold; color: ${config.color}; margin-left: 10px;">${estimatedTime} minutes</span>
            </div>
          ` : ''}
        </div>

        <!-- Contact -->
        <div style="text-align: center; padding: 20px 0; border-top: 1px solid #e5e7eb;">
          <p style="margin: 0 0 15px 0; color: #6b7280;">Need help with your order?</p>
          <a href="tel:${process.env.OWNER_PHONE || '+237XXXXXXXXX'}" class="button">Contact Support</a>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer" style="background-color: #1f2937; color: white; padding: 20px; text-align: center;">
        <h3 style="margin: 0 0 10px 0; color: #f97316;">Zina Chop House</h3>
        <p style="margin: 0; font-size: 14px; opacity: 0.8;">📞 ${process.env.OWNER_PHONE || '+237 XXX XXX XXX'}</p>
      </div>
    `;

    return baseTemplate(content, `Order Update - ${orderId}`);
  },

  /**
   * Special Order Notification Template (for owner)
   */
  specialOrderNotification: (data) => {
    const { customerName, customerPhone, customerEmail, orderType, details, eventDate, numberOfPeople, notes, submissionDate } = data;

    const content = `
      <!-- Header -->
      <div class="header" style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: 30px; text-align: center;">
        <div style="font-size: 48px; margin-bottom: 10px;">🔔</div>
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">New Special Order Request</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Action Required</p>
      </div>

      <!-- Content -->
      <div class="content" style="padding: 30px;">
        <!-- Customer Info -->
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
          <h2 style="margin: 0 0 15px 0; color: #1f2937;">Customer Information</h2>
          <div style="margin-bottom: 10px;">
            <span style="color: #6b7280; font-weight: 500;">Name:</span>
            <span style="margin-left: 10px; font-weight: bold;">${customerName}</span>
          </div>
          <div style="margin-bottom: 10px;">
            <span style="color: #6b7280; font-weight: 500;">Phone:</span>
            <a href="tel:${customerPhone}" style="margin-left: 10px; color: #f97316; text-decoration: none; font-weight: bold;">${customerPhone}</a>
          </div>
          <div style="margin-bottom: 10px;">
            <span style="color: #6b7280; font-weight: 500;">Email:</span>
            <a href="mailto:${customerEmail}" style="margin-left: 10px; color: #f97316; text-decoration: none;">${customerEmail}</a>
          </div>
          <div>
            <span style="color: #6b7280; font-weight: 500;">Submitted:</span>
            <span style="margin-left: 10px;">${submissionDate || new Date().toLocaleString()}</span>
          </div>
        </div>

        <!-- Order Details -->
        <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b; margin-bottom: 25px;">
          <h3 style="margin: 0 0 15px 0; color: #92400e;">Order Details</h3>
          <div style="margin-bottom: 15px;">
            <span style="color: #92400e; font-weight: 500;">Type:</span>
            <span style="margin-left: 10px; font-weight: bold; text-transform: capitalize;">${orderType.replace('_', ' ')}</span>
          </div>
          ${eventDate ? `
            <div style="margin-bottom: 15px;">
              <span style="color: #92400e; font-weight: 500;">Event Date:</span>
              <span style="margin-left: 10px; font-weight: bold;">${eventDate}</span>
            </div>
          ` : ''}
          ${numberOfPeople ? `
            <div style="margin-bottom: 15px;">
              <span style="color: #92400e; font-weight: 500;">Number of People:</span>
              <span style="margin-left: 10px; font-weight: bold;">${numberOfPeople}</span>
            </div>
          ` : ''}
          <div style="margin-top: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #92400e;">Order Description:</h4>
            <div style="background-color: white; padding: 15px; border-radius: 6px; border: 1px solid #f59e0b;">
              <p style="margin: 0; color: #1f2937; line-height: 1.6;">${details}</p>
            </div>
          </div>
          ${notes ? `
            <div style="margin-top: 15px;">
              <h4 style="margin: 0 0 10px 0; color: #92400e;">Additional Notes:</h4>
              <div style="background-color: white; padding: 15px; border-radius: 6px; border: 1px solid #f59e0b;">
                <p style="margin: 0; color: #1f2937; line-height: 1.6;">${notes}</p>
              </div>
            </div>
          ` : ''}
        </div>

        <!-- Action Required -->
        <div style="background-color: #fee2e2; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin-bottom: 25px;">
          <h3 style="margin: 0 0 15px 0; color: #dc2626;">⚠️ Action Required</h3>
          <p style="margin: 0 0 15px 0; color: #dc2626; line-height: 1.6;">
            Please contact the customer to confirm the order details, discuss pricing, and arrange the delivery/pickup.
          </p>
          <div style="text-align: center; margin-top: 20px;">
            <a href="tel:${customerPhone}" class="button" style="background-color: #dc2626; margin-right: 10px;">Call Customer</a>
            <a href="mailto:${customerEmail}" class="button" style="background-color: #059669;">Send Email</a>
          </div>
        </div>
      </div>
    `;

    return baseTemplate(content, `Special Order Request - ${orderType}`);
  },

  /**
   * Welcome Email Template
   */
  welcome: (data) => {
    const { customerName, customerEmail } = data;

    const content = `
      <!-- Header -->
      <div class="header" style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; padding: 40px; text-align: center;">
        <div style="font-size: 48px; margin-bottom: 15px;">🎉</div>
        <h1 style="margin: 0; font-size: 32px; font-weight: bold;">Welcome to Zina Chop House!</h1>
        <p style="margin: 15px 0 0 0; font-size: 18px; opacity: 0.9;">Hello ${customerName}, we're excited to serve you!</p>
      </div>

      <!-- Content -->
      <div class="content" style="padding: 40px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h2 style="color: #1f2937; margin-bottom: 15px;">Authentic Cameroonian Cuisine Awaits</h2>
          <p style="color: #6b7280; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            Thank you for joining our family! We're passionate about bringing you the most delicious and authentic Cameroonian dishes, 
            prepared with love and the finest ingredients.
          </p>
        </div>

        <!-- Features -->
        <div style="margin-bottom: 30px;">
          <h3 style="color: #1f2937; margin-bottom: 20px; text-align: center;">What Makes Us Special</h3>
          <div style="display: grid; gap: 20px;">
            <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b;">
              <h4 style="margin: 0 0 10px 0; color: #92400e;">🍽️ Authentic Recipes</h4>
              <p style="margin: 0; color: #92400e;">Traditional Cameroonian dishes prepared with authentic recipes passed down through generations.</p>
            </div>
            <div style="background-color: #dcfce7; padding: 20px; border-radius: 8px; border-left: 4px solid #22c55e;">
              <h4 style="margin: 0 0 10px 0; color: #166534;">🚚 Fast Delivery</h4>
              <p style="margin: 0; color: #166534;">Quick and reliable delivery service to bring hot, fresh meals right to your door.</p>
            </div>
            <div style="background-color: #dbeafe; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6;">
              <h4 style="margin: 0 0 10px 0; color: #1e40af;">⭐ Quality Guaranteed</h4>
              <p style="margin: 0; color: #1e40af;">We use only the freshest ingredients and maintain the highest quality standards.</p>
            </div>
          </div>
        </div>

        <!-- CTA -->
        <div style="text-align: center; padding: 30px 0; background-color: #f9fafb; border-radius: 8px;">
          <h3 style="margin: 0 0 15px 0; color: #1f2937;">Ready to Order?</h3>
          <p style="margin: 0 0 20px 0; color: #6b7280;">Explore our menu and place your first order today!</p>
          <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/menu" class="button">Browse Menu</a>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer" style="background-color: #1f2937; color: white; padding: 25px; text-align: center;">
        <h3 style="margin: 0 0 15px 0; color: #f97316;">Zina Chop House</h3>
        <p style="margin: 0 0 10px 0; font-size: 14px; opacity: 0.8;">Authentic Cameroonian Cuisine</p>
        <p style="margin: 0 0 10px 0; font-size: 14px; opacity: 0.8;">📞 ${process.env.OWNER_PHONE || '+237 XXX XXX XXX'}</p>
        <p style="margin: 0; font-size: 12px; opacity: 0.6;">
          You're receiving this email because you signed up for Zina Chop House.
        </p>
      </div>
    `;

    return baseTemplate(content, 'Welcome to Zina Chop House');
  }
};

export default emailTemplates;
