export declare class TransactionHistory {
    'amount'?: number;
    'balanceExpirationDate'?: string;
    'cancelledAt'?: string;
    'completedAt'?: string;
    'createdAt'?: string;
    'id'?: string;
    'meta'?: {
        [key: string]: any;
    };
    'rejectReason'?: string;
    'rejectedAt'?: string;
    'status'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
