import api from './api.js';

/**
 * Order Service - Handles all order-related API calls
 */
class OrderService {
  /**
   * Create a new order
   */
  async createOrder(orderData) {
    try {
      const response = await api.post('/orders', orderData);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  /**
   * Get user's orders
   */
  async getUserOrders(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      
      const queryString = params.toString();
      const url = queryString ? `/orders/my-orders?${queryString}` : '/orders/my-orders';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw error;
    }
  }

  /**
   * Get a specific order by ID
   */
  async getOrder(orderId) {
    try {
      const response = await api.get(`/orders/${orderId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  /**
   * Update order status (admin/delivery only)
   */
  async updateOrderStatus(orderId, status, notes = '') {
    try {
      const response = await api.put(`/orders/${orderId}/status`, { status, notes });
      return response.data;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  /**
   * Cancel an order
   */
  async cancelOrder(orderId, reason = '') {
    try {
      const response = await api.put(`/orders/${orderId}/cancel`, { reason });
      return response.data;
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw error;
    }
  }

  /**
   * Calculate delivery cost
   */
  async calculateDelivery(address, orderValue = 0) {
    try {
      const response = await api.post('/orders/calculate-delivery', { address, orderValue });
      return response.data;
    } catch (error) {
      console.error('Error calculating delivery:', error);
      throw error;
    }
  }

  /**
   * Get order tracking information
   */
  async trackOrder(orderId) {
    try {
      const response = await api.get(`/orders/${orderId}/track`);
      return response.data;
    } catch (error) {
      console.error('Error tracking order:', error);
      throw error;
    }
  }

  /**
   * Get order statistics (admin only)
   */
  async getOrderStats(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.status) params.append('status', filters.status);
      
      const queryString = params.toString();
      const url = queryString ? `/orders/stats?${queryString}` : '/orders/stats';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching order stats:', error);
      throw error;
    }
  }

  /**
   * Get all orders (admin only)
   */
  async getAllOrders(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.customerId) params.append('customerId', filters.customerId);
      if (filters.deliveryPartnerId) params.append('deliveryPartnerId', filters.deliveryPartnerId);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);
      
      const queryString = params.toString();
      const url = queryString ? `/orders?${queryString}` : '/orders';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching all orders:', error);
      throw error;
    }
  }

  /**
   * Assign delivery partner to order (admin only)
   */
  async assignDeliveryPartner(orderId, deliveryPartnerId) {
    try {
      const response = await api.put(`/orders/${orderId}/assign-delivery`, { deliveryPartnerId });
      return response.data;
    } catch (error) {
      console.error('Error assigning delivery partner:', error);
      throw error;
    }
  }

  /**
   * Get delivery partner orders (delivery partner only)
   */
  async getDeliveryOrders(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      
      const queryString = params.toString();
      const url = queryString ? `/orders/delivery-orders?${queryString}` : '/orders/delivery-orders';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching delivery orders:', error);
      throw error;
    }
  }

  /**
   * Update delivery status (delivery partner only)
   */
  async updateDeliveryStatus(orderId, status, location = null) {
    try {
      const response = await api.put(`/orders/${orderId}/delivery-status`, { status, location });
      return response.data;
    } catch (error) {
      console.error('Error updating delivery status:', error);
      throw error;
    }
  }

  /**
   * Get order receipt
   */
  async getOrderReceipt(orderId) {
    try {
      const response = await api.get(`/orders/${orderId}/receipt`);
      return response.data;
    } catch (error) {
      console.error('Error fetching order receipt:', error);
      throw error;
    }
  }

  /**
   * Reorder (create new order based on previous order)
   */
  async reorder(orderId) {
    try {
      const response = await api.post(`/orders/${orderId}/reorder`);
      return response.data;
    } catch (error) {
      console.error('Error reordering:', error);
      throw error;
    }
  }

  /**
   * Rate and review order
   */
  async rateOrder(orderId, rating, review = '') {
    try {
      const response = await api.post(`/orders/${orderId}/rate`, { rating, review });
      return response.data;
    } catch (error) {
      console.error('Error rating order:', error);
      throw error;
    }
  }

  /**
   * Get order history with analytics
   */
  async getOrderHistory(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.status) params.append('status', filters.status);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      
      const queryString = params.toString();
      const url = queryString ? `/orders/history?${queryString}` : '/orders/history';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching order history:', error);
      throw error;
    }
  }

  /**
   * Get popular items based on orders
   */
  async getPopularItems(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.period) params.append('period', filters.period);
      if (filters.limit) params.append('limit', filters.limit);
      
      const queryString = params.toString();
      const url = queryString ? `/orders/popular-items?${queryString}` : '/orders/popular-items';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching popular items:', error);
      throw error;
    }
  }

  /**
   * Validate order before creation
   */
  async validateOrder(orderData) {
    try {
      const response = await api.post('/orders/validate', orderData);
      return response.data;
    } catch (error) {
      console.error('Error validating order:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
const orderService = new OrderService();
export default orderService;
