export declare class UpdateTierGroupRequest {
    'name': string;
    'tierOrder': Array<string>;
    'upgradeStrategy': UpdateTierGroupRequest.UpgradeStrategyEnum;
    'downgradeStrategy': UpdateTierGroupRequest.DowngradeStrategyEnum;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace UpdateTierGroupRequest {
    enum UpgradeStrategyEnum {
        RealTime,
        MembershipAnniversary,
        TierAnniversary
    }
    enum DowngradeStrategyEnum {
        RealTime,
        MembershipAnniversary,
        TierAnniversary
    }
}
