/**
 * Brevo API
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity | 
 *
 * The version of the OpenAPI document: 3.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from './models';

export class UpdateCouponCollectionRequest {
    /**
    * A default coupon to be used in case there are no coupons left
    */
    'defaultCoupon'?: string;
    /**
    * Specify an expiration date for the coupon collection in RFC3339 format. Use null to remove the expiration date.
    */
    'expirationDate'?: Date;
    /**
    * Send a notification alert (email) when the remaining days until the expiration date are equal or fall bellow this number. Use null to disable alerts.
    */
    'remainingDaysAlert'?: number;
    /**
    * Send a notification alert (email) when the remaining coupons count is equal or fall bellow this number. Use null to disable alerts.
    */
    'remainingCouponsAlert'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "defaultCoupon",
            "baseName": "defaultCoupon",
            "type": "string"
        },
        {
            "name": "expirationDate",
            "baseName": "expirationDate",
            "type": "Date"
        },
        {
            "name": "remainingDaysAlert",
            "baseName": "remainingDaysAlert",
            "type": "number"
        },
        {
            "name": "remainingCouponsAlert",
            "baseName": "remainingCouponsAlert",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return UpdateCouponCollectionRequest.attributeTypeMap;
    }
}

