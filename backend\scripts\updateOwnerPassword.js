import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import User from '../models/User.js';
import dotenv from 'dotenv';

dotenv.config();

const updateOwnerPassword = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Find the owner
    const owner = await User.findOne({ role: 'owner' });
    if (!owner) {
      console.log('❌ No owner found in database');
      return;
    }
    
    console.log(`📋 Found owner: ${owner.email}`);
    
    // Update password to 123456
    const hashedPassword = await bcrypt.hash('123456', 12);
    await User.updateOne(
      { _id: owner._id },
      { password: hashedPassword }
    );
    
    console.log('✅ Owner password updated to: 123456');
    console.log('🔑 Login credentials: <EMAIL> / 123456');
    
  } catch (error) {
    console.error('❌ Error updating owner password:', error);
  } finally {
    mongoose.disconnect();
  }
};

updateOwnerPassword();
