import { GetWebhookAuth } from './getWebhookAuth';
import { GetWebhookHeadersInner } from './getWebhookHeadersInner';
export declare class UpdateWebhook {
    'url'?: string;
    'description'?: string;
    'events'?: Array<UpdateWebhook.EventsEnum>;
    'domain'?: string;
    'batched'?: boolean;
    'auth'?: GetWebhookAuth;
    'headers'?: Array<GetWebhookHeadersInner>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace UpdateWebhook {
    enum EventsEnum {
        Sent,
        HardBounce,
        SoftBounce,
        Blocked,
        Spam,
        Delivered,
        Request,
        Click,
        Invalid,
        Deferred,
        Opened,
        UniqueOpened,
        Unsubscribed,
        ListAddition,
        ContactUpdated,
        ContactDeleted,
        InboundEmailProcessed
    }
}
