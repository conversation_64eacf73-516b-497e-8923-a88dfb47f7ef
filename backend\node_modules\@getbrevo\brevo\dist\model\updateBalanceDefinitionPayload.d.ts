export declare class UpdateBalanceDefinitionPayload {
    'balanceAvailabilityDurationModifier'?: UpdateBalanceDefinitionPayload.BalanceAvailabilityDurationModifierEnum;
    'balanceAvailabilityDurationUnit'?: UpdateBalanceDefinitionPayload.BalanceAvailabilityDurationUnitEnum;
    'balanceAvailabilityDurationValue'?: number;
    'balanceExpirationDate'?: string;
    'balanceOptionAmountOvertakingStrategy'?: UpdateBalanceDefinitionPayload.BalanceOptionAmountOvertakingStrategyEnum;
    'balanceOptionCreditRounding'?: UpdateBalanceDefinitionPayload.BalanceOptionCreditRoundingEnum;
    'balanceOptionDebitRounding'?: UpdateBalanceDefinitionPayload.BalanceOptionDebitRoundingEnum;
    'description'?: string;
    'imageRef'?: string;
    'maxAmount'?: number;
    'maxCreditAmountLimit'?: number;
    'maxDebitAmountLimit'?: number;
    'meta'?: {
        [key: string]: any;
    };
    'minAmount'?: number;
    'name': string;
    'unit': UpdateBalanceDefinitionPayload.UnitEnum;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace UpdateBalanceDefinitionPayload {
    enum BalanceAvailabilityDurationModifierEnum {
        NoModification,
        StartOfPeriod,
        EndOfPeriod
    }
    enum BalanceAvailabilityDurationUnitEnum {
        Day,
        Week,
        Month,
        Year
    }
    enum BalanceOptionAmountOvertakingStrategyEnum {
        Strict,
        Partial
    }
    enum BalanceOptionCreditRoundingEnum {
        Lower,
        Upper,
        Natural
    }
    enum BalanceOptionDebitRoundingEnum {
        Lower,
        Upper,
        Natural
    }
    enum UnitEnum {
        Points,
        Eur,
        Usd,
        Mxn,
        Gbp,
        Inr,
        Cad,
        Sgd,
        Ron,
        Jpy,
        Myr,
        Clp,
        Pen,
        Mad,
        Aud,
        Chf,
        Brl
    }
}
