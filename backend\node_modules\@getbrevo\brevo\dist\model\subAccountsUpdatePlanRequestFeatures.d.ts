export declare class SubAccountsUpdatePlanRequestFeatures {
    'users'?: number;
    'landingPage'?: number;
    'salesUsers'?: number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
