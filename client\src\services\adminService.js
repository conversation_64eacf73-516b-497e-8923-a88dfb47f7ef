import api from './api.js';

/**
 * Admin Service - Handles all admin-related API calls
 */
class AdminService {
  /**
   * Get admin dashboard statistics
   */
  async getStats() {
    try {
      const response = await api.get('/admin/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching admin stats:', error);
      throw error;
    }
  }

  /**
   * Create a new user (admin function)
   */
  async createUser(userData) {
    try {
      const response = await api.post('/admin/users', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Get all users with filtering
   */
  async getUsers(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.role) params.append('role', filters.role);
      if (filters.isActive !== undefined) params.append('isActive', filters.isActive);
      if (filters.search) params.append('search', filters.search);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/users?${queryString}` : '/admin/users';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Update user role
   */
  async updateUserRole(userId, role) {
    try {
      const response = await api.put(`/admin/users/${userId}/role`, { role });
      return response.data;
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  }

  /**
   * Toggle user active status
   */
  async toggleUserStatus(userId, isActive) {
    try {
      const response = await api.put(`/admin/users/${userId}/status`, { isActive });
      return response.data;
    } catch (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }
  }

  /**
   * Delete user (soft delete)
   */
  async deleteUser(userId) {
    try {
      const response = await api.delete(`/admin/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Get all orders (admin view)
   */
  async getOrders(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.customerId) params.append('customerId', filters.customerId);
      if (filters.deliveryPartnerId) params.append('deliveryPartnerId', filters.deliveryPartnerId);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/orders?${queryString}` : '/admin/orders';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.role) params.append('role', filters.role);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/users/analytics?${queryString}` : '/admin/users/analytics';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      throw error;
    }
  }

  /**
   * Get order analytics
   */
  async getOrderAnalytics(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.period) params.append('period', filters.period);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/orders/analytics?${queryString}` : '/admin/orders/analytics';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching order analytics:', error);
      throw error;
    }
  }

  /**
   * Get revenue analytics
   */
  async getRevenueAnalytics(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.period) params.append('period', filters.period);
      if (filters.groupBy) params.append('groupBy', filters.groupBy);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/revenue/analytics?${queryString}` : '/admin/revenue/analytics';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      throw error;
    }
  }

  /**
   * Export data
   */
  async exportData(type, filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.format) params.append('format', filters.format);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/export/${type}?${queryString}` : `/admin/export/${type}`;
      
      const response = await api.get(url, {
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  /**
   * Send bulk notifications
   */
  async sendBulkNotification(notificationData) {
    try {
      const response = await api.post('/admin/notifications/bulk', notificationData);
      return response.data;
    } catch (error) {
      console.error('Error sending bulk notification:', error);
      throw error;
    }
  }

  /**
   * Get system health
   */
  async getSystemHealth() {
    try {
      const response = await api.get('/admin/system/health');
      return response.data;
    } catch (error) {
      console.error('Error fetching system health:', error);
      throw error;
    }
  }

  /**
   * Update system settings
   */
  async updateSystemSettings(settings) {
    try {
      const response = await api.put('/admin/system/settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating system settings:', error);
      throw error;
    }
  }

  /**
   * Get system logs
   */
  async getSystemLogs(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.level) params.append('level', filters.level);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      
      const queryString = params.toString();
      const url = queryString ? `/admin/system/logs?${queryString}` : '/admin/system/logs';
      
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching system logs:', error);
      throw error;
    }
  }

  /**
   * Backup database
   */
  async backupDatabase() {
    try {
      const response = await api.post('/admin/system/backup');
      return response.data;
    } catch (error) {
      console.error('Error backing up database:', error);
      throw error;
    }
  }

  /**
   * Get delivery zones management
   */
  async getDeliveryZones() {
    try {
      const response = await api.get('/admin/delivery-zones');
      return response.data;
    } catch (error) {
      console.error('Error fetching delivery zones:', error);
      throw error;
    }
  }

  /**
   * Update delivery zones
   */
  async updateDeliveryZones(zones) {
    try {
      const response = await api.put('/admin/delivery-zones', { zones });
      return response.data;
    } catch (error) {
      console.error('Error updating delivery zones:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
const adminService = new AdminService();
export default adminService;
