/**
 * Brevo API
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity | 
 *
 * The version of the OpenAPI document: 3.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from './models';
import { GetExternalFeedByUUIDHeadersInner } from './getExternalFeedByUUIDHeadersInner';

export class UpdateExternalFeed {
    /**
    * Name of the feed
    */
    'name'?: string;
    /**
    * URL of the feed
    */
    'url'?: string;
    /**
    * Auth type of the feed:   * `basic`   * `token`   * `noAuth` 
    */
    'authType'?: UpdateExternalFeed.AuthTypeEnum;
    /**
    * Username for authType `basic`
    */
    'username'?: string;
    /**
    * Password for authType `basic`
    */
    'password'?: string;
    /**
    * Token for authType `token`
    */
    'token'?: string;
    /**
    * Custom headers for the feed
    */
    'headers'?: Array<GetExternalFeedByUUIDHeadersInner>;
    /**
    * Maximum number of retries on the feed url
    */
    'maxRetries'?: number;
    /**
    * Toggle caching of feed url response
    */
    'cache'?: boolean = false;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "url",
            "baseName": "url",
            "type": "string"
        },
        {
            "name": "authType",
            "baseName": "authType",
            "type": "UpdateExternalFeed.AuthTypeEnum"
        },
        {
            "name": "username",
            "baseName": "username",
            "type": "string"
        },
        {
            "name": "password",
            "baseName": "password",
            "type": "string"
        },
        {
            "name": "token",
            "baseName": "token",
            "type": "string"
        },
        {
            "name": "headers",
            "baseName": "headers",
            "type": "Array<GetExternalFeedByUUIDHeadersInner>"
        },
        {
            "name": "maxRetries",
            "baseName": "maxRetries",
            "type": "number"
        },
        {
            "name": "cache",
            "baseName": "cache",
            "type": "boolean"
        }    ];

    static getAttributeTypeMap() {
        return UpdateExternalFeed.attributeTypeMap;
    }
}

export namespace UpdateExternalFeed {
    export enum AuthTypeEnum {
        Basic = <any> 'basic',
        Token = <any> 'token',
        NoAuth = <any> 'noAuth'
    }
}
