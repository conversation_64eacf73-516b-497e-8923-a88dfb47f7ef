# Zina Chop House Setup Guide

## Quick Start

### 1. Database Setup
First, make sure MongoDB is running on your system, then seed the database:

```bash
# Navigate to backend directory
cd backend

# Install dependencies (if not already done)
npm install

# Seed the database with owner, delivery person, and menu items
npm run seed

# Optional: Seed only menu items
npm run seed:menu
```

### 2. Start the Backend Server
```bash
# In the backend directory
npm run dev
```
The backend will start on http://localhost:5000

### 3. Start the Frontend Client
```bash
# Navigate to client directory (in a new terminal)
cd client

# Install dependencies (if not already done)
npm install

# Start the development server
npm run dev
```
The frontend will start on http://localhost:5173

## Initial Login Credentials

After seeding the database, you can use these credentials:

- **Owner**: <EMAIL> / admin123
- **Delivery**: <EMAIL> / password123

## User Registration

- **Customers** can register directly through the application interface
- **Owners** are created only through database seeding (no registration interface)
- **Delivery Partners** can be added by the owner through the admin panel
- **Order History** will be empty initially as no orders exist in the fresh database

## Features Available

### For Customers (after registration):
- Browse menu items
- Add items to cart
- Place orders
- Track order status
- View order history (initially empty)
- Leave feedback

### For Owners:
- Manage menu items
- View and manage orders
- Manage users (customers and delivery partners)
- View analytics and reports
- Manage special orders

### For Delivery Partners:
- View assigned deliveries
- Update delivery status
- Track delivery history

## Database Structure

The seeded database includes:
- **Users**: 1 Owner + 1 Delivery Partner
- **Menu Items**: Comprehensive menu with various categories:
  - Rice Dishes (Jollof Rice, Fried Rice, Coconut Rice)
  - Traditional Dishes (Ndole, Water Fufu & Eru, Achu)
  - Grilled Items (Tilapia, Suya, Chicken)
  - Soups (Pepper Soup, Fish Soup)
  - Sides (Plantains, Yam, Beans)
  - Beverages (Palm Wine, Zobo, Fruit Juice)
  - Snacks (Akara, Moi Moi, Puff Puff)
- **Orders**: None (clean slate for testing)
- **Feedback**: None (clean slate)

## Testing the Application

1. **Registration Flow**: Create a new customer account
2. **Menu Browsing**: Explore the various menu categories
3. **Order Placement**: Add items to cart and place an order
4. **Owner Functions**: Login as owner to manage the system
5. **Delivery Functions**: Login as delivery partner to handle orders

## Troubleshooting

- Ensure MongoDB is running before starting the backend
- Check that ports 5000 (backend) and 5173 (frontend) are available
- Verify environment variables are properly set in `.env` files
- Check browser console for any client-side errors
- Check terminal output for server-side errors

## Next Steps

- Customize menu items through the owner dashboard
- Test the complete order flow from customer to delivery
- Configure payment methods (MTN MoMo, Orange Money)
- Set up email notifications through Brevo
- Configure image uploads through Cloudinary (optional)
