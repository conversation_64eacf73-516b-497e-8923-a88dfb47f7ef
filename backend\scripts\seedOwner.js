import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import User from '../models/User.js';
import dotenv from 'dotenv';

dotenv.config();

const seedOwner = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);

    // Check if owner already exists
    const existingOwner = await User.findOne({ role: 'owner' });
    if (existingOwner) {
      console.log('Owner already exists');
      return;
    }

    // Create owner account with simple password
    const hashedPassword = await bcrypt.hash('123456', 12);

    const owner = new User({
      name: '<PERSON><PERSON><PERSON> House Owner',
      email: process.env.OWNER_EMAIL || '<EMAIL>',
      phone: process.env.OWNER_PHONE || '+************',
      password: hashedPassword,
      role: 'owner',
      emailVerified: true,
      phoneVerified: true,
      isActive: true
    });

    await owner.save();
    console.log('Owner account created successfully');
    console.log('Login credentials: <EMAIL> / 123456');

  } catch (error) {
    console.error('Error seeding owner:', error);
  } finally {
    mongoose.disconnect();
  }
};

seedOwner();