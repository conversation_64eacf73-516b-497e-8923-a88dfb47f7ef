import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import User from '../models/User.js';
import emailService from '../services/emailService.js';
import dotenv from 'dotenv';

dotenv.config();

const seedOwner = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);

    // Check if owner already exists
    const existingOwner = await User.findOne({ role: 'owner' });
    if (existingOwner) {
      console.log('Owner already exists');
      return;
    }

    // Create owner account with simple password
    const password = '123456';
    const hashedPassword = await bcrypt.hash(password, 12);
    const ownerEmail = '<EMAIL>';
    const ownerName = 'Silas Matala';

    const owner = new User({
      name: ownerName,
      email: ownerEmail,
      phone: process.env.OWNER_PHONE || '+************',
      password: hashedPassword,
      role: 'owner',
      emailVerified: true,
      phoneVerified: true,
      isActive: true
    });

    await owner.save();
    console.log('✅ Owner account created successfully');
    console.log(`🔑 Login credentials: ${ownerEmail} / ${password}`);

    // Send welcome email
    try {
      // Wait a bit for email service to initialize
      await new Promise(resolve => setTimeout(resolve, 2000));

      await emailService.sendOwnerWelcomeEmail({
        ownerEmail,
        ownerName,
        password
      });
      console.log('📧 Welcome email sent successfully to owner');
    } catch (emailError) {
      console.warn('⚠️ Failed to send welcome email:', emailError.message);
      console.log('Owner account created but email notification failed');
    }

  } catch (error) {
    console.error('❌ Error seeding owner:', error);
  } finally {
    mongoose.disconnect();
  }
};

seedOwner();