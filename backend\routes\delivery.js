import express from 'express';
import { body, validationResult } from 'express-validator';
import Order from '../models/Order.js';
import User from '../models/User.js';
import { authenticate, authorize } from '../middleware/auth.js';

const router = express.Router();

// Middleware to check if user is delivery partner
const checkDeliveryRole = (req, res, next) => {
  if (req.user.role !== 'delivery' && req.user.role !== 'owner') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Delivery partner role required.'
    });
  }
  next();
};

// @desc    Get delivery partner stats
// @route   GET /api/delivery/stats
// @access  Private (Delivery)
router.get('/stats', authenticate, checkDeliveryRole, async (req, res) => {
  try {
    const deliveryPartnerId = req.user.id;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get stats
    const totalDeliveries = await Order.countDocuments({
      deliveryPartnerId,
      status: 'delivered'
    });

    const pendingDeliveries = await Order.countDocuments({
      deliveryPartnerId,
      status: { $in: ['confirmed', 'preparing', 'ready', 'out-for-delivery'] }
    });

    const completedToday = await Order.countDocuments({
      deliveryPartnerId,
      status: 'delivered',
      updatedAt: { $gte: today, $lt: tomorrow }
    });

    // Calculate earnings (assuming 10% commission)
    const deliveredOrders = await Order.find({
      deliveryPartnerId,
      status: 'delivered'
    });

    const earnings = deliveredOrders.reduce((total, order) => {
      return total + (order.totalAmount * 0.1); // 10% commission
    }, 0);

    res.json({
      success: true,
      data: {
        totalDeliveries,
        pendingDeliveries,
        completedToday,
        earnings: Math.round(earnings)
      }
    });
  } catch (error) {
    console.error('Get delivery stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get delivery stats',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @desc    Get delivery partner dashboard stats
// @route   GET /api/delivery/dashboard
// @access  Private (Delivery)
router.get('/dashboard', authenticate, checkDeliveryRole, async (req, res) => {
  try {
    const deliveryPartnerId = req.user.id;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get today's stats
    const todayOrders = await Order.find({
      deliveryPartnerId,
      createdAt: { $gte: today, $lt: tomorrow }
    });

    // Get all-time stats
    const totalOrders = await Order.countDocuments({ deliveryPartnerId });
    const completedOrders = await Order.countDocuments({ 
      deliveryPartnerId, 
      status: 'delivered' 
    });

    // Get active orders
    const activeOrders = await Order.find({
      deliveryPartnerId,
      status: { $in: ['confirmed', 'preparing', 'ready', 'out-for-delivery'] }
    }).populate('userId', 'name phone')
      .populate('items.menuItem', 'name')
      .sort({ createdAt: -1 });

    // Calculate earnings (assuming 10% commission)
    const totalEarnings = await Order.aggregate([
      {
        $match: {
          deliveryPartnerId: req.user._id,
          status: 'delivered'
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: { $multiply: ['$totalAmount', 0.1] } }
        }
      }
    ]);

    const todayEarnings = todayOrders
      .filter(order => order.status === 'delivered')
      .reduce((sum, order) => sum + (order.totalAmount * 0.1), 0);

    res.json({
      success: true,
      data: {
        stats: {
          todayOrders: todayOrders.length,
          todayEarnings: Math.round(todayEarnings),
          totalOrders,
          completedOrders,
          totalEarnings: totalEarnings[0]?.total || 0,
          completionRate: totalOrders > 0 ? Math.round((completedOrders / totalOrders) * 100) : 0
        },
        activeOrders: activeOrders.slice(0, 5), // Latest 5 active orders
        recentActivity: todayOrders.slice(0, 10) // Latest 10 today's orders
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard data',
      error: error.message
    });
  }
});

// @desc    Get assigned orders for delivery partner
// @route   GET /api/delivery/orders
// @access  Private (Delivery)
router.get('/orders', authenticate, checkDeliveryRole, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const deliveryPartnerId = req.user.id;

    let query = { deliveryPartnerId };
    
    if (status) {
      if (status === 'active') {
        query.status = { $in: ['confirmed', 'preparing', 'ready', 'out-for-delivery'] };
      } else {
        query.status = status;
      }
    }

    const orders = await Order.find(query)
      .populate('userId', 'name phone email')
      .populate('items.menuItem', 'name images')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
});

// @desc    Get specific order details for delivery
// @route   GET /api/delivery/orders/:id
// @access  Private (Delivery)
router.get('/orders/:id', authenticate, checkDeliveryRole, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('userId', 'name phone email')
      .populate('items.menuItem', 'name images price')
      .populate('deliveryPartnerId', 'name phone');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if this delivery partner is assigned to this order
    if (order.deliveryPartnerId._id.toString() !== req.user.id && req.user.role !== 'owner') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order details',
      error: error.message
    });
  }
});

// @desc    Update order status by delivery partner
// @route   PUT /api/delivery/orders/:id/status
// @access  Private (Delivery)
router.put('/orders/:id/status', authenticate, checkDeliveryRole, [
  body('status').isIn(['confirmed', 'preparing', 'ready', 'out-for-delivery', 'delivered'])
    .withMessage('Invalid status'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes too long')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, notes, location } = req.body;
    
    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if this delivery partner is assigned to this order
    if (order.deliveryPartnerId.toString() !== req.user.id && req.user.role !== 'owner') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Validate status transition
    const validTransitions = {
      'confirmed': ['preparing'],
      'preparing': ['ready'],
      'ready': ['out-for-delivery'],
      'out-for-delivery': ['delivered']
    };

    if (order.status !== status && !validTransitions[order.status]?.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Cannot change status from ${order.status} to ${status}`
      });
    }

    // Update order
    order.status = status;
    if (notes) {
      order.deliveryNotes = notes;
    }
    if (location) {
      order.deliveryLocation = location;
    }

    // Set delivery timestamps
    if (status === 'out-for-delivery' && !order.pickedUpAt) {
      order.pickedUpAt = new Date();
    }
    if (status === 'delivered' && !order.deliveredAt) {
      order.deliveredAt = new Date();
    }

    await order.save();

    // Populate for response
    await order.populate('userId', 'name phone email');
    await order.populate('items.menuItem', 'name');

    res.json({
      success: true,
      message: `Order status updated to ${status}`,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: error.message
    });
  }
});

// @desc    Accept order assignment
// @route   POST /api/delivery/orders/:id/accept
// @access  Private (Delivery)
router.post('/orders/:id/accept', authenticate, checkDeliveryRole, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if order is available for assignment
    if (order.deliveryPartnerId) {
      return res.status(400).json({
        success: false,
        message: 'Order already assigned to another delivery partner'
      });
    }

    if (order.status !== 'confirmed') {
      return res.status(400).json({
        success: false,
        message: 'Order is not available for delivery assignment'
      });
    }

    // Assign delivery partner
    order.deliveryPartnerId = req.user.id;
    order.assignedAt = new Date();
    await order.save();

    await order.populate('userId', 'name phone');
    await order.populate('items.menuItem', 'name');

    res.json({
      success: true,
      message: 'Order accepted successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to accept order',
      error: error.message
    });
  }
});

// @desc    Get available orders for assignment
// @route   GET /api/delivery/available-orders
// @access  Private (Delivery)
router.get('/available-orders', authenticate, checkDeliveryRole, async (req, res) => {
  try {
    const orders = await Order.find({
      status: 'confirmed',
      deliveryPartnerId: { $exists: false }
    })
    .populate('userId', 'name phone')
    .populate('items.menuItem', 'name')
    .sort({ createdAt: -1 })
    .limit(20);

    res.json({
      success: true,
      data: orders
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch available orders',
      error: error.message
    });
  }
});

// @desc    Get delivery partner earnings
// @route   GET /api/delivery/earnings
// @access  Private (Delivery)
router.get('/earnings', authenticate, checkDeliveryRole, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const deliveryPartnerId = req.user.id;

    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        deliveredAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }

    const earnings = await Order.aggregate([
      {
        $match: {
          deliveryPartnerId: req.user._id,
          status: 'delivered',
          ...dateFilter
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$deliveredAt' },
            month: { $month: '$deliveredAt' },
            day: { $dayOfMonth: '$deliveredAt' }
          },
          totalOrders: { $sum: 1 },
          totalRevenue: { $sum: '$totalAmount' },
          totalEarnings: { $sum: { $multiply: ['$totalAmount', 0.1] } }
        }
      },
      {
        $sort: { '_id.year': -1, '_id.month': -1, '_id.day': -1 }
      }
    ]);

    const totalEarnings = earnings.reduce((sum, day) => sum + day.totalEarnings, 0);
    const totalOrders = earnings.reduce((sum, day) => sum + day.totalOrders, 0);

    res.json({
      success: true,
      data: {
        summary: {
          totalEarnings: Math.round(totalEarnings),
          totalOrders,
          averageEarningsPerOrder: totalOrders > 0 ? Math.round(totalEarnings / totalOrders) : 0
        },
        dailyEarnings: earnings
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch earnings',
      error: error.message
    });
  }
});

export default router;
