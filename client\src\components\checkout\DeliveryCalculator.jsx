import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MapPinIcon, 
  ClockIcon, 
  TruckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  GiftIcon
} from '@heroicons/react/24/outline';
import { debounce } from 'lodash';
import orderService from '../../services/orderService';

const DeliveryCalculator = ({ 
  address, 
  orderValue, 
  onDeliveryUpdate,
  register,
  errors,
  watch
}) => {
  const { t } = useTranslation();
  const [deliveryData, setDeliveryData] = useState(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [calculationError, setCalculationError] = useState(null);
  const [availableZones, setAvailableZones] = useState([]);
  const [showRecommendations, setShowRecommendations] = useState(false);

  // Watch for address changes
  const watchedAddress = watch('street') + ' ' + watch('city');

  // Debounced delivery calculation
  const calculateDelivery = useCallback(
    debounce(async (address, orderValue) => {
      if (!address || !orderValue || address.trim().length < 3) {
        setDeliveryData(null);
        onDeliveryUpdate(null);
        return;
      }

      setIsCalculating(true);
      setCalculationError(null);

      try {
        const response = await orderService.calculateDelivery(address.trim(), orderValue);

        if (response.success) {
          const deliveryInfo = response.data;
          setDeliveryData(deliveryInfo);
          onDeliveryUpdate(deliveryInfo.delivery);

          // Show recommendations if there are savings opportunities
          if (deliveryInfo.recommendations && deliveryInfo.recommendations.length > 0) {
            setShowRecommendations(true);
          }
        } else {
          throw new Error(response.message || 'Failed to calculate delivery');
        }
      } catch (error) {
        console.error('Delivery calculation error:', error);
        setCalculationError(error.response?.data?.message || error.message || 'Failed to calculate delivery fee');
        setDeliveryData(null);
        onDeliveryUpdate(null);
      } finally {
        setIsCalculating(false);
      }
    }, 500),
    [onDeliveryUpdate]
  );

  // Load available zones on component mount
  useEffect(() => {
    const loadZones = async () => {
      try {
        const response = await axios.get('/api/orders/delivery-zones');
        if (response.data.success) {
          setAvailableZones(response.data.data);
        }
      } catch (error) {
        console.error('Failed to load delivery zones:', error);
      }
    };

    loadZones();
  }, []);

  // Calculate delivery when address or order value changes
  useEffect(() => {
    if (watchedAddress && orderValue) {
      calculateDelivery(watchedAddress, orderValue);
    }
  }, [watchedAddress, orderValue, calculateDelivery]);

  const getDeliveryStatusColor = () => {
    if (calculationError) return 'text-red-600';
    if (deliveryData?.delivery?.available) return 'text-green-600';
    return 'text-yellow-600';
  };

  const getDeliveryStatusIcon = () => {
    if (calculationError) return ExclamationTriangleIcon;
    if (deliveryData?.delivery?.available) return CheckCircleIcon;
    return InformationCircleIcon;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        {t('deliveryInformation')}
      </h2>

      {/* Address Input */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPinIcon className="h-4 w-4 inline mr-1" />
            {t('deliveryAddress')} *
          </label>
          <input
            {...register('street', { 
              required: t('addressRequired'),
              minLength: { value: 3, message: t('addressTooShort') }
            })}
            type="text"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder={t('enterDeliveryAddress')}
          />
          {errors.street && (
            <p className="text-red-500 text-sm mt-1">{errors.street.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('city')} *
          </label>
          <input
            {...register('city', { required: t('cityRequired') })}
            type="text"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="Douala"
            defaultValue="Douala"
          />
          {errors.city && (
            <p className="text-red-500 text-sm mt-1">{errors.city.message}</p>
          )}
        </div>
      </div>

      {/* Delivery Calculation Results */}
      <AnimatePresence>
        {isCalculating && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
          >
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
              <span className="text-blue-800">{t('calculatingDelivery')}</span>
            </div>
          </motion.div>
        )}

        {calculationError && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-4 p-4 bg-red-50 rounded-lg border border-red-200"
          >
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-3" />
              <span className="text-red-800">{calculationError}</span>
            </div>
          </motion.div>
        )}

        {deliveryData && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <TruckIcon className="h-5 w-5 text-green-600 mr-2" />
                <span className="font-medium text-green-900">
                  {t('deliveryAvailable')}
                </span>
              </div>
              <span className="text-sm text-green-700">
                {deliveryData.delivery.distance}km
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-3">
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-orange-600">
                  {deliveryData.delivery.fee.toLocaleString()} XAF
                </div>
                <div className="text-xs text-gray-600">{t('deliveryFee')}</div>
                {deliveryData.delivery.discount > 0 && (
                  <div className="text-xs text-green-600">
                    {deliveryData.delivery.discount}% {t('discount')}
                  </div>
                )}
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-blue-600">
                  {deliveryData.delivery.estimatedTime} min
                </div>
                <div className="text-xs text-gray-600">{t('estimatedTime')}</div>
              </div>
            </div>

            {deliveryData.delivery.discountReason && (
              <div className="text-sm text-green-700 bg-green-100 p-2 rounded">
                <GiftIcon className="h-4 w-4 inline mr-1" />
                {deliveryData.delivery.discountReason}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Delivery Recommendations */}
      {showRecommendations && deliveryData?.recommendations && deliveryData.recommendations.length > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mb-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200"
        >
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-yellow-900">
              <GiftIcon className="h-4 w-4 inline mr-1" />
              {t('savingsOpportunities')}
            </h3>
            <button
              type="button"
              onClick={() => setShowRecommendations(false)}
              className="text-yellow-600 hover:text-yellow-800"
            >
              ×
            </button>
          </div>
          <div className="space-y-2">
            {deliveryData.recommendations.map((rec, index) => (
              <div key={index} className="text-sm text-yellow-800 bg-yellow-100 p-2 rounded">
                {rec.message} - {t('save')} {rec.savings.toLocaleString()} XAF
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Available Delivery Zones */}
      {availableZones.length > 0 && (
        <div className="mt-6">
          <h3 className="font-medium text-gray-900 mb-3">{t('deliveryZones')}</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-32 overflow-y-auto">
            {availableZones.map((zone) => (
              <div key={zone._id} className="text-sm p-2 bg-gray-50 rounded border">
                <div className="font-medium">{zone.name}</div>
                <div className="text-gray-600 text-xs">
                  {zone.baseFee.toLocaleString()} XAF • {zone.estimatedDeliveryTime} min
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DeliveryCalculator;
