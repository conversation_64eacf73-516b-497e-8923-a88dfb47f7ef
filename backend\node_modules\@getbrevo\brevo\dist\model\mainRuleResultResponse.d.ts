import { MainResultParameterResponse } from './mainResultParameterResponse';
export declare class MainRuleResultResponse {
    'action'?: string;
    'parameters'?: Array<MainResultParameterResponse>;
    'service'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
