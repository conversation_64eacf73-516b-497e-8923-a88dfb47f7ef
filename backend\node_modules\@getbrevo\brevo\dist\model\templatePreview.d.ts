export declare class TemplatePreview {
    'fromEmail'?: string;
    'fromName'?: string;
    'html'?: string;
    'subject'?: string;
    'usedFeedNames'?: Array<string>;
    'previewText'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
