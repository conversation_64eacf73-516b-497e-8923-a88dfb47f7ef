import { MemberContact } from './memberContact';
import { SubscriptionAttributedReward } from './subscriptionAttributedReward';
import { SubscriptionBalances } from './subscriptionBalances';
import { SubscriptionTier } from './subscriptionTier';
export declare class SubscriptionHandlerInfo {
    'balance'?: SubscriptionBalances;
    'members'?: Array<MemberContact>;
    'reward'?: Array<SubscriptionAttributedReward>;
    'tier'?: Array<SubscriptionTier>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
