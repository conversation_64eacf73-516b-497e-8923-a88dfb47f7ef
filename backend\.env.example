# Database
MONGODB_URI=mongodb://localhost:27017/zina-chop-house

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Client URL
CLIENT_URL=http://localhost:5173

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Email Configuration
# Brevo (Primary email service)
BREVO_API_KEY=your-brevo-api-key

# Nodemailer (Fallback email service)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# WhatsApp Business API
WHATSAPP_PHONE_NUMBER_ID=your-whatsapp-phone-number-id
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token

# Server
PORT=5000
NODE_ENV=development

# Owner contact info
OWNER_EMAIL=<EMAIL>
OWNER_PHONE=+237123456789
OWNER_WHATSAPP=+237123456789

# Owner authentication
OWNER_SECRET_KEY=ZinaChopHouse2024!@#

# MTN Mobile Money Configuration (Cameroon)
MTN_ENVIRONMENT=sandbox
MTN_API_BASE_URL=https://sandbox.momodeveloper.mtn.com
MTN_SUBSCRIPTION_KEY=your-mtn-subscription-key
MTN_API_USER=your-mtn-api-user-id
MTN_API_KEY=your-mtn-api-key

# Orange Money Configuration (Cameroon)
ORANGE_ENVIRONMENT=sandbox
ORANGE_API_BASE_URL=https://api.orange.com
ORANGE_CLIENT_ID=your-orange-client-id
ORANGE_CLIENT_SECRET=your-orange-client-secret
ORANGE_MERCHANT_KEY=your-orange-merchant-key

# Business Configuration
BUSINESS_NAME=Zina Chop House
BUSINESS_ADDRESS=Douala, Cameroon
DEFAULT_DELIVERY_FEE=500
FREE_DELIVERY_THRESHOLD=10000

# API Configuration
API_URL=http://localhost:5000
FRONTEND_URL=http://localhost:5173
