import { AggregateBalance } from './aggregateBalance';
export declare class ModelSubscriptionBalanceResp {
    'balance'?: Array<AggregateBalance>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
