import { SubscriptionAggregateBalance } from './subscriptionAggregateBalance';
export declare class SubscriptionBalances {
    'balances'?: Array<SubscriptionAggregateBalance>;
    'contactId'?: number;
    'loyaltyProgramId'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
