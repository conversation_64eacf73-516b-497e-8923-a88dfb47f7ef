/**
 * Brevo API
 * Brevo provide a RESTFul API that can be used with any languages. With this API, you will be able to :   - Manage your campaigns and get the statistics   - Manage your contacts   - Send transactional Emails and SMS   - and much more...  You can download our wrappers at https://github.com/orgs/brevo  **Possible responses**   | Code | Message |   | :-------------: | ------------- |   | 200  | OK. Successful Request  |   | 201  | OK. Successful Creation |   | 202  | OK. Request accepted |   | 204  | OK. Successful Update/Deletion  |   | 400  | Error. Bad Request  |   | 401  | Error. Authentication Needed  |   | 402  | Error. Not enough credit, plan upgrade needed  |   | 403  | Error. Permission denied  |   | 404  | Error. Object does not exist |   | 405  | Error. Method not allowed  |   | 406  | Error. Not Acceptable  |   | 422  | Error. Unprocessable Entity | 
 *
 * The version of the OpenAPI document: 3.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from './models';

export class MasterDetailsResponsePlanInfoFeaturesInner {
    /**
    * Name of the feature
    */
    'name'?: string;
    /**
    * Unit value of the feature
    */
    'unitValue'?: string;
    /**
    * Quantity provided in the plan
    */
    'quantity'?: number;
    /**
    * Quantity with overages provided in the plan (only applicable on ENTv2)
    */
    'quantityWithOverages'?: number;
    /**
    * Quantity consumed by master
    */
    'used'?: number;
    /**
    * Quantity consumed by sub-organizations over the admin plan limit (only applicable on ENTv2)
    */
    'usedOverages'?: number;
    /**
    * Quantity remaining in the plan
    */
    'remaining'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "unitValue",
            "baseName": "unitValue",
            "type": "string"
        },
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        },
        {
            "name": "quantityWithOverages",
            "baseName": "quantityWithOverages",
            "type": "number"
        },
        {
            "name": "used",
            "baseName": "used",
            "type": "number"
        },
        {
            "name": "usedOverages",
            "baseName": "usedOverages",
            "type": "number"
        },
        {
            "name": "remaining",
            "baseName": "remaining",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return MasterDetailsResponsePlanInfoFeaturesInner.attributeTypeMap;
    }
}

