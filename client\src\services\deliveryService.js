import api from './api';

const deliveryService = {
  // Get delivery dashboard stats
  getDeliveryStats: async () => {
    try {
      const response = await api.get('/delivery/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching delivery stats:', error);
      throw error;
    }
  },

  // Get assigned orders for delivery partner
  getAssignedOrders: async () => {
    try {
      const response = await api.get('/delivery/orders');
      return response.data;
    } catch (error) {
      console.error('Error fetching assigned orders:', error);
      throw error;
    }
  },

  // Update order status
  updateOrderStatus: async (orderId, status, location = null) => {
    try {
      const response = await api.put(`/delivery/orders/${orderId}/status`, {
        status,
        location
      });
      return response.data;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  },

  // Accept order for delivery
  acceptOrder: async (orderId) => {
    try {
      const response = await api.post(`/delivery/orders/${orderId}/accept`);
      return response.data;
    } catch (error) {
      console.error('Error accepting order:', error);
      throw error;
    }
  },

  // Mark order as picked up
  markPickedUp: async (orderId) => {
    try {
      const response = await api.put(`/delivery/orders/${orderId}/pickup`);
      return response.data;
    } catch (error) {
      console.error('Error marking order as picked up:', error);
      throw error;
    }
  },

  // Mark order as delivered
  markDelivered: async (orderId, deliveryNotes = '') => {
    try {
      const response = await api.put(`/delivery/orders/${orderId}/deliver`, {
        deliveryNotes
      });
      return response.data;
    } catch (error) {
      console.error('Error marking order as delivered:', error);
      throw error;
    }
  },

  // Update delivery partner availability
  updateAvailability: async (isAvailable) => {
    try {
      const response = await api.put('/delivery/availability', {
        isAvailable
      });
      return response.data;
    } catch (error) {
      console.error('Error updating availability:', error);
      throw error;
    }
  },

  // Get delivery history
  getDeliveryHistory: async (page = 1, limit = 10) => {
    try {
      const response = await api.get(`/delivery/history?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching delivery history:', error);
      throw error;
    }
  },

  // Get earnings summary
  getEarnings: async (period = 'week') => {
    try {
      const response = await api.get(`/delivery/earnings?period=${period}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching earnings:', error);
      throw error;
    }
  }
};

export default deliveryService;
