export declare class OrderIdentifiers {
    'extId'?: string;
    'loyaltySubscriptionId'?: string;
    'phoneId'?: string;
    'emailId'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
