export declare class UpdateEmailCampaignEmailExpirationDate {
    'duration'?: number;
    'unit'?: UpdateEmailCampaignEmailExpirationDate.UnitEnum;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace UpdateEmailCampaignEmailExpirationDate {
    enum UnitEnum {
        Days,
        Weeks,
        Months
    }
}
