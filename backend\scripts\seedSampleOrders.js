import mongoose from 'mongoose';
import Order from '../models/Order.js';
import User from '../models/User.js';
import MenuItem from '../models/MenuItem.js';
import dotenv from 'dotenv';q

dotenv.config();

const seedSampleOrders = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    
    // Get users
    const customer = await User.findOne({ role: 'customer' });
    const delivery = await User.findOne({ role: 'delivery' });
    const menuItems = await MenuItem.find().limit(3);
    
    if (!customer || !delivery || menuItems.length === 0) {
      console.log('❌ Missing required data. Please run seed:users and seed:real-menu first');
      return;
    }
    
    // Clear existing orders
    await Order.deleteMany({});
    console.log('🗑️ Cleared existing orders');
    
    // Create sample orders
    const orders = [
      {
        orderNumber: `ZCH${Date.now()}001`,
        customer: customer._id,
        items: [
          {
            menuItem: menuItems[0]._id,
            quantity: 2,
            price: menuItems[0].price,
            specialInstructions: 'Extra spicy please'
          },
          {
            menuItem: menuItems[1]._id,
            quantity: 1,
            price: menuItems[1].price
          }
        ],
        totalAmount: (menuItems[0].price * 2) + menuItems[1].price,
        status: 'confirmed',
        deliveryInfo: {
          type: 'delivery',
          address: {
            street: '123 Main Street',
            city: 'Douala',
            state: 'Littoral',
            country: 'Cameroon',
            coordinates: {
              latitude: 4.0511,
              longitude: 9.7679
            }
          },
          estimatedDeliveryTime: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes from now
        },
        paymentMethod: 'cash-on-delivery',
        assignedDeliveryPartner: delivery._id
      },
      {
        orderNumber: `ZCH${Date.now()}002`,
        customer: customer._id,
        items: [
          {
            menuItem: menuItems[2]._id,
            quantity: 1,
            price: menuItems[2].price,
            specialInstructions: 'No onions'
          }
        ],
        totalAmount: menuItems[2].price,
        status: 'out-for-delivery',
        deliveryInfo: {
          type: 'delivery',
          address: {
            street: '456 Oak Avenue',
            city: 'Douala',
            state: 'Littoral',
            country: 'Cameroon',
            coordinates: {
              latitude: 4.0611,
              longitude: 9.7779
            }
          },
          estimatedDeliveryTime: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes from now
        },
        paymentMethod: 'mtn-mobile-money',
        assignedDeliveryPartner: delivery._id
      },
      {
        orderNumber: `ZCH${Date.now()}003`,
        customer: customer._id,
        items: [
          {
            menuItem: menuItems[0]._id,
            quantity: 1,
            price: menuItems[0].price
          }
        ],
        totalAmount: menuItems[0].price,
        status: 'delivered',
        deliveryInfo: {
          type: 'delivery',
          address: {
            street: '789 Pine Street',
            city: 'Douala',
            state: 'Littoral',
            country: 'Cameroon'
          },
          actualDeliveryTime: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
        },
        paymentMethod: 'cash-on-delivery',
        paymentStatus: 'paid',
        assignedDeliveryPartner: delivery._id
      }
    ];

    console.log('📦 Creating sample orders...');
    const createdOrders = await Order.insertMany(orders);
    
    console.log('✅ Sample orders created successfully!');
    console.log(`📊 Created ${createdOrders.length} orders:`);
    createdOrders.forEach(order => {
      console.log(`  - ${order.orderNumber}: ${order.status}`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding sample orders:', error);
  } finally {
    mongoose.disconnect();
  }
};

seedSampleOrders();
